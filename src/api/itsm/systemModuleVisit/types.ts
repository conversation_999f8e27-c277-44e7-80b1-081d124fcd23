export interface SystemModuleVisitVO {
  /**
   * 主键id
   */
  visitModuleId: string | number;

  /**
   * 系统id
   */
  systemId: string | number;

  /**
   * 模块id
   */
  moduleId: string | number;

  /**
   * 系统访问次数
   */
  visitCount: string;

  /**
   * 访问日期
   */
  visitDate: string;

  /**
   * 模块名称
   */
  moduleName: string;
}

export interface SystemModuleVisitForm extends BaseEntity {
  /**
   * 主键id
   */
  visitModuleId?: string | number;

  /**
   * 系统id
   */
  systemId?: string | number;

  /**
   * 模块id
   */
  moduleId?: string | number;

  /**
   * 系统访问次数
   */
  visitCount?: string;

  /**
   * 访问日期
   */
  visitDate?: string;
}

export interface SystemModuleVisitQuery extends PageQuery {
  /**
   * 系统id
   */
  systemId?: string | number;

  /**
   * 模块id
   */
  moduleId?: string | number;

  /**
   * 系统访问次数
   */
  visitCount?: string;

  /**
   * 访问日期
   */
  visitDate?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
