export interface AttendanceGoOutLogVO {
  /**
   * 外出记录id
   */
  goOutId: string | number;

  /**
   * 所属项目id
   */
  projectId: string | number;

  /**
   * 外出理由
   */
  reason: string;

  /**
   * 外出时间
   */
  goOutTime: string;
}

export interface AttendanceGoOutLogForm extends BaseEntity {
  /**
   * 外出记录id
   */
  goOutId?: string | number;

  /**
   * 所属项目id
   */
  projectId?: string | number;

  /**
   * 外出理由
   */
  reason?: string;

  /**
   * 外出时间
   */
  goOutTime?: string;

  /**
   * 办理状态
   */
  processingStatus?: string;
}

export interface AttendanceGoOutLogQuery extends PageQuery {
  /**
   * 所属项目id
   */
  projectId?: string | number;

  /**
   * 外出理由
   */
  reason?: string;

  /**
   * 外出时间
   */
  goOutTime?: string;

  /**
   * 用户id
   */
  sysUserId?: string | number;

  /**
   * 日期范围参数
   */
  params?: any;
}
