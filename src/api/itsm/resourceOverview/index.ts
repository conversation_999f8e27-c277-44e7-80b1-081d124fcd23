import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { ResourceOverviewVO, ResourceOverviewForm, ResourceOverviewQuery } from '@/api/itsm/resourceOverview/types';

/**
 * 查询资源总览信息列表
 * @param query
 * @returns {*}
 */

export const listResourceOverview = (query?: ResourceOverviewQuery): AxiosPromise<ResourceOverviewVO[]> => {
  return request({
    url: '/itsm/resourceOverview/list',
    method: 'get',
    params: query
  });
};
