export interface SystemVisitVO {
  /**
   * 主键id
   */
  visitId: string | number;

  /**
   * 系统id
   */
  systemId: string | number;

  /**
   * 系统访问次数
   */
  visitCount: string;

  /**
   * 访问日期
   */
  visitDate: string;

  /**
   * 系统名称
   */
  systemName: string;
}

export interface SystemVisitForm extends BaseEntity {
  /**
   * 主键id
   */
  visitId: string | number;
}

export interface SystemVisitQuery extends PageQuery {
  /**
   * 系统id
   */
  systemId?: string | number;

  /**
   * 访问日期
   */
  visitDate?: string;

  /**
   * 系统名称
   */
  systemName?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
