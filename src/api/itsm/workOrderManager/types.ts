import { NodeConfigVO } from '@/api/workflow/nodeConfig/types';
import { DefinitionConfigVO } from '@/api/workflow/definitionConfig/types';

export interface WorkOrderManagerVO {
  /**
   * 服务器id
   */
  orderId: string | number;

  /**
   * 工单编号
   */
  orderNo: string;

  /**
   * 所属项目id
   */
  projectId: string | number;

  projectName: string;

  systemName: string;

  systemId?: string | number;

  /**
   * 工单标题
   */
  orderTitle: string;

  /**
   * 工单类型
   */
  type: string;

  /**
   * 二级工单类型
   */
  subType: string;

  /**
   * 紧急情况
   */
  emergency: string;

  /**
   * 反馈时间
   */
  feedbackTime: string;

  /**
   * 反馈时间辅助手工编辑字段
   */
  feedbackTime2: string;

  /**
   * 联系人姓名
   */
  linkman: string;

  /**
   * 联系人电话
   */
  linkmanPhone: string;

  /**
   * 联系人所属单位
   */
  workLinkmanCompany: string;

  /**
   * 微信\QQ\邮件
   */
  wcQqEmail: string;

  /**
   * 详细描述
   */
  orderContent: string;

  /**
   * 附件id
   */
  fileId: string | number;

  /**
   * 主要办理人
   */
  mainSysUserId: string | number;

  /**
   * 其他办理人
   */
  otherSysUserId: string | number;

  /**
   * 办结\取消时间
   */
  overCancleTime: string;

  /**
   * 办结\取消时间辅助手工编辑字段
   */
  overCancleTime2: string;

  /**
   * 工作量(小时)
   */
  workload: string;

  /**
   * 办理状态
   */
  processingStatus: string;

  /**
   * 处理情况
   */
  handlingSituation: string;

  /**
   * 处理方式
   */
  handleMethod: string;

  wfNodeConfigVo?: NodeConfigVO;
  wfDefinitionConfigVo?: DefinitionConfigVO;

  businessKey?: string;

  taskId?: string;
}

export interface WorkOrderManagerForm extends BaseEntity {
  /**
   * 服务器id
   */
  orderId?: string | number;

  /**
   * 工单编号
   */
  orderNo?: string;

  /**
   * 所属项目id
   */
  projectId?: string | number;

  systemId?: string | number;

  /**
   * 工单标题
   */
  orderTitle?: string;

  /**
   * 工单类型
   */
  type?: string;

  /**
   * 二级工单类型
   */
  subType?: string;

  /**
   * 紧急情况
   */
  emergency?: string;

  /**
   * 反馈时间
   */
  feedbackTime?: string;

  /**
   * 反馈时间辅助手工编辑字段
   */
  feedbackTime2?: string;

  /**
   * 联系人姓名
   */
  linkman?: string;

  /**
   * 联系人电话
   */
  linkmanPhone?: string;

  /**
   * 联系人所属单位
   */
  workLinkmanCompany?: string;

  /**
   * 微信\QQ\邮件
   */
  wcQqEmail?: string;

  /**
   * 详细描述
   */
  orderContent?: string;

  /**
   * 附件id
   */
  fileId?: string | number;

  /**
   * 主要办理人
   */
  mainSysUserId?: string | number;

  /**
   * 其他办理人
   */
  otherSysUserId?: string | number;

  /**
   * 办结\取消时间
   */
  overCancleTime?: string;

  /**
   * 办结\取消时间辅助手工编辑字段
   */
  overCancleTime2?: string;

  /**
   * 工作量(小时)
   */
  workload?: string;

  /**
   * 办理状态
   */
  processingStatus?: string;

  /**
   * 处理情况
   */
  handlingSituation?: string;

  /**
   * 处理方式
   */
  handleMethod?: string;
}

export interface WorkOrderManagerQuery extends PageQuery {
  /**
   * 工单编号
   */
  orderNo?: string;

  /**
   * 所属项目id
   */
  projectId?: string | number;

  systemId?: string | number;

  /**
   * 工单标题
   */
  orderTitle?: string;

  /**
   * 工单类型
   */
  type?: string;

  /**
   * 二级工单类型
   */
  subType?: string;

  /**
   * 紧急情况
   */
  emergency?: string;

  /**
   * 反馈时间
   */
  feedbackTime?: string;

  /**
   * 反馈时间辅助手工编辑字段
   */
  feedbackTime2?: string;

  /**
   * 联系人姓名
   */
  linkman?: string;

  /**
   * 联系人电话
   */
  linkmanPhone?: string;

  /**
   * 联系人所属单位
   */
  workLinkmanCompany?: string;

  /**
   * 微信\QQ\邮件
   */
  wcQqEmail?: string;

  /**
   * 详细描述
   */
  orderContent?: string;

  /**
   * 附件id
   */
  fileId?: string | number;

  /**
   * 主要办理人
   */
  mainSysUserId?: string | number;

  /**
   * 其他办理人
   */
  otherSysUserId?: string | number;

  /**
   * 办结\取消时间
   */
  overCancleTime?: string;

  /**
   * 办结\取消时间辅助手工编辑字段
   */
  overCancleTime2?: string;

  /**
   * 工作量(小时)
   */
  workload?: string;

  /**
   * 办理状态
   */
  processingStatus?: string;

  /**
   * 处理情况
   */
  handlingSituation?: string;

  /**
   * 处理方式
   */
  handleMethod?: string;

  /**
   * 页面路由参数
   */
  searchType?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
