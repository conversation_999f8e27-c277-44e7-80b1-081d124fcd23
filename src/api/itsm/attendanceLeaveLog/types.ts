export interface AttendanceLeaveLogVO {
  /**
   * 请假记录id
   */
  leaveId: string | number;

  /**
   * 请假类型：1-年假，2-事假，3-病假，4-事假，5-调休
   */
  type: string;

  /**
   * 请假开始时间
   */
  startTime: string;

  /**
   * 请假结束时间
   */
  endTime: string;
}

export interface AttendanceLeaveLogForm extends BaseEntity {
  /**
   * 请假记录id
   */
  leaveId?: string | number;

  /**
   * 请假类型：1-年假，2-事假，3-病假，4-事假，5-调休
   */
  type?: string;

  /**
   * 请假开始时间
   */
  startTime?: string;

  /**
   * 请假结束时间
   */
  endTime?: string;

  /**
   * 附件id
   */
  fileId?: string | number;

  /**
   * 办理状态
   */
  processingStatus?: string;
}

export interface AttendanceLeaveLogQuery extends PageQuery {
  /**
   * 请假类型：1-年假，2-事假，3-病假，4-事假，5-调休
   */
  type?: string;

  /**
   * 请假开始时间
   */
  startTime?: string;

  /**
   * 请假结束时间
   */
  endTime?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
