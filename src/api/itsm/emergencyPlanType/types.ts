export interface EmergencyPlanTypeVO {
  /**
   *
   */
  planTypeId: string | number;

  /**
   * 状态（0正常 1停用）
   */
  status: string;

  /**
   * 类型名称
   */
  name: string;
}

export interface EmergencyPlanTypeForm extends BaseEntity {
  /**
   *
   */
  planTypeId?: string | number;

  /**
   * 状态（0正常 1停用）
   */
  status?: string;

  /**
   * 类型名称
   */
  name?: string;

  /**
   * 列表
   */
  planTypeIds?: Array<string | number>;
}

export interface EmergencyPlanTypeQuery extends PageQuery {
  /**
   * 状态（0正常 1停用）
   */
  status?: string;

  /**
   * 类型名称
   */
  name?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
