export interface FundInfoVO {
  /**
   * 资金id
   */
  fundId: string | number;

  /**
   * 所属项目id
   */
  projectId: string | number;

  /**
   * 款项批次
   */
  paymentBatch: string;

  /**
   * 计划付款金额
   */
  planAmount: number;

  /**
   * 计划付款日期
   */
  planPaymentTime: string;

  /**
   * 已付款金额
   */
  paidAmount: string | number;

  /**
   * 实际付款日期
   */
  actualPaymentTime: string;
}

export interface FundInfoForm extends BaseEntity {
  /**
   * 资金id
   */
  fundId?: string | number;

  /**
   * 所属项目id
   */
  projectId?: string | number;

  /**
   * 款项批次
   */
  paymentBatch?: string;

  /**
   * 计划付款金额
   */
  planAmount?: number;

  /**
   * 计划付款日期
   */
  planPaymentTime?: string;

  /**
   * 已付款金额
   */
  paidAmount?: string | number;

  /**
   * 实际付款日期
   */
  actualPaymentTime?: string;
}

export interface FundInfoQuery extends PageQuery {
  /**
   * 所属项目id
   */
  projectId?: string | number;

  /**
   * 款项批次
   */
  paymentBatch?: string;

  /**
   * 计划付款金额
   */
  planAmount?: number;

  /**
   * 计划付款日期
   */
  planPaymentTime?: string;

  /**
   * 已付款金额
   */
  paidAmount?: string | number;

  /**
   * 实际付款日期
   */
  actualPaymentTime?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
