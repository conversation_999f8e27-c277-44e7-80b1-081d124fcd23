import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import {
  AttendanceWorkOvertimeLogVO,
  AttendanceWorkOvertimeLogForm,
  AttendanceWorkOvertimeLogQuery
} from '@/api/itsm/attendanceWorkOvertimeLog/types';

/**
 * 查询加班申请列表
 * @param query
 * @returns {*}
 */

export const listAttendanceWorkOvertimeLog = (query?: AttendanceWorkOvertimeLogQuery): AxiosPromise<AttendanceWorkOvertimeLogVO[]> => {
  return request({
    url: '/itsm/attendanceWorkOvertimeLog/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询加班申请详细
 * @param overtimeId
 */
export const getAttendanceWorkOvertimeLog = (overtimeId: string | number): AxiosPromise<AttendanceWorkOvertimeLogVO> => {
  return request({
    url: '/itsm/attendanceWorkOvertimeLog/' + overtimeId,
    method: 'get'
  });
};

/**
 * 新增加班申请
 * @param data
 */
export const addAttendanceWorkOvertimeLog = (data: AttendanceWorkOvertimeLogForm) => {
  return request({
    url: '/itsm/attendanceWorkOvertimeLog',
    method: 'post',
    data: data
  });
};

/**
 * 修改加班申请
 * @param data
 */
export const updateAttendanceWorkOvertimeLog = (data: AttendanceWorkOvertimeLogForm) => {
  return request({
    url: '/itsm/attendanceWorkOvertimeLog',
    method: 'put',
    data: data
  });
};

/**
 * 删除加班申请
 * @param overtimeId
 */
export const delAttendanceWorkOvertimeLog = (overtimeId: string | number | Array<string | number>) => {
  return request({
    url: '/itsm/attendanceWorkOvertimeLog/' + overtimeId,
    method: 'delete'
  });
};
