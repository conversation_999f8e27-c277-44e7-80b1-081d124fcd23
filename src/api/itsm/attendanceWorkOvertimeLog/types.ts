export interface AttendanceWorkOvertimeLogVO {
  /**
   * 加班id
   */
  overtimeId: string | number;

  /**
   * 所属项目id
   */
  projectId: string | number;

  /**
   * 加班理由
   */
  reason: string;

  /**
   * 加班开始时间
   */
  startTime: string;

  /**
   * 加班结束时间
   */
  endTime: string;

  /**
   * 办理状态
   */
  processingStatus?: string;

  /**
   * 用户id
   */
  sysUserId?: string | number;
}

export interface AttendanceWorkOvertimeLogForm extends BaseEntity {
  /**
   * 加班id
   */
  overtimeId?: string | number;

  /**
   * 所属项目id
   */
  projectId?: string | number;

  /**
   * 加班理由
   */
  reason?: string;

  /**
   * 加班开始时间
   */
  startTime?: string;

  /**
   * 加班结束时间
   */
  endTime?: string;

  /**
   * 办理状态
   */
  processingStatus?: string;
}

export interface AttendanceWorkOvertimeLogQuery extends PageQuery {
  /**
   * 所属项目id
   */
  projectId?: string | number;

  /**
   * 加班理由
   */
  reason?: string;

  /**
   * 加班开始时间
   */
  startTime?: string;

  /**
   * 加班结束时间
   */
  endTime?: string;

  /**
   * 日期范围参数
   */
  params?: any;

  /**
   * 用户id
   */
  sysUserId?: string | number;
}
