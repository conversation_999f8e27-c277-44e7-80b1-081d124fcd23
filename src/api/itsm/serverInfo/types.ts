export interface ServerInfoVO {
  /**
   * 服务器id
   */
  serverId: string | number;

  /**
   * 所属系统id
   */
  systemId: string | number;

  /**
   * 服务器名称
   */
  serverName: string;

  /**
   * 服务器类型
   */
  serverType: string;

  /**
   * 其他类型
   */
  otherType: string;

  /**
   * 是否部署中山市政务云平台
   */
  zsGovCloudFlag: string;

  /**
   * 云主机托管位置
   */
  cloudLocation: string;

  /**
   * 操作系统名称及版本
   */
  osNameVersion: string;

  /**
   * 开发工具
   */
  devTool: string;

  /**
   * 数据库名称及版本
   */
  dbNameVersion: string;

  /**
   * 数据库容量
   */
  dbCapacity: string;

  /**
   * 中间件名称及版本
   */
  middlewareNameVersion: string | number;

  /**
   * 其他软件
   */
  otherSoftware: string;

  /**
   * 党政网络ip
   */
  govNetworkIp: string;

  /**
   * 互联网ip
   */
  internetIp: string;

  /**
   * 云主机ipv4地址
   */
  cloudServerIpv4: string;

  /**
   * 云主机ipv6地址
   */
  cloudServerIpv6: string;

  /**
   * 互联网访问地址
   */
  internetUrl: string;

  /**
   * 政务网络访问地址
   */
  govNetworkUrl: string;

  /**
   * 系统后台地址
   */
  systemAdminUrl: string;

  /**
   * cpu（核）
   */
  cpu: string;

  /**
   * 内存（G）
   */
  memory: string;

  /**
   * 系统盘（G）
   */
  systemHardDisk: string;

  /**
   * 数据盘（G）
   */
  dataHardDisk: string;

  /**
   * 申请工单号
   */
  applyOrderNo: string;

  /**
   * 开通端口信息
   */
  openPortInfo: string;

  /**
   * 端口用途
   */
  openPortUse: string;
}

export interface ServerInfoForm extends BaseEntity {
  /**
   * 服务器id
   */
  serverId?: string | number;

  /**
   * 所属系统id
   */
  systemId?: string | number;

  /**
   * 服务器名称
   */
  serverName?: string;

  /**
   * 服务器类型
   */
  serverType?: string;

  /**
   * 其他类型
   */
  otherType?: string;

  /**
   * 是否部署中山市政务云平台
   */
  zsGovCloudFlag?: string;

  /**
   * 云主机托管位置
   */
  cloudLocation?: string;

  /**
   * 操作系统名称及版本
   */
  osNameVersion?: string;

  /**
   * 开发工具
   */
  devTool?: string;

  /**
   * 数据库名称及版本
   */
  dbNameVersion?: string;

  /**
   * 数据库容量
   */
  dbCapacity?: string;

  /**
   * 中间件名称及版本
   */
  middlewareNameVersion?: string | number;

  /**
   * 其他软件
   */
  otherSoftware?: string;

  /**
   * 党政网络ip
   */
  govNetworkIp?: string;

  /**
   * 互联网ip
   */
  internetIp?: string;

  /**
   * 云主机ipv4地址
   */
  cloudServerIpv4?: string;

  /**
   * 云主机ipv6地址
   */
  cloudServerIpv6?: string;

  /**
   * 互联网访问地址
   */
  internetUrl?: string;

  /**
   * 政务网络访问地址
   */
  govNetworkUrl?: string;

  /**
   * 系统后台地址
   */
  systemAdminUrl?: string;

  /**
   * cpu（核）
   */
  cpu?: string;

  /**
   * 内存（G）
   */
  memory?: string;

  /**
   * 系统盘（G）
   */
  systemHardDisk?: string;

  /**
   * 数据盘（G）
   */
  dataHardDisk?: string;

  /**
   * 申请工单号
   */
  applyOrderNo?: string;

  /**
   * 开通端口信息
   */
  openPortInfo?: string;

  /**
   * 端口用途
   */
  openPortUse?: string;
}

export interface ServerInfoQuery extends PageQuery {
  /**
   * 所属系统id
   */
  systemId?: string | number;

  /**
   * 所属系统id列表
   */
  systemIds?: string[];

  /**
   * 服务器名称
   */
  serverName?: string;

  /**
   * 服务器类型
   */
  serverType?: string;

  /**
   * 其他类型
   */
  otherType?: string;

  /**
   * 是否部署中山市政务云平台
   */
  zsGovCloudFlag?: string;

  /**
   * 云主机托管位置
   */
  cloudLocation?: string;

  /**
   * 操作系统名称及版本
   */
  osNameVersion?: string;

  /**
   * 开发工具
   */
  devTool?: string;

  /**
   * 数据库名称及版本
   */
  dbNameVersion?: string;

  /**
   * 数据库容量
   */
  dbCapacity?: string;

  /**
   * 中间件名称及版本
   */
  middlewareNameVersion?: string | number;

  /**
   * 其他软件
   */
  otherSoftware?: string;

  /**
   * 党政网络ip
   */
  govNetworkIp?: string;

  /**
   * 互联网ip
   */
  internetIp?: string;

  /**
   * 云主机ipv4地址
   */
  cloudServerIpv4?: string;

  /**
   * 云主机ipv6地址
   */
  cloudServerIpv6?: string;

  /**
   * 互联网访问地址
   */
  internetUrl?: string;

  /**
   * 政务网络访问地址
   */
  govNetworkUrl?: string;

  /**
   * 系统后台地址
   */
  systemAdminUrl?: string;

  /**
   * cpu（核）
   */
  cpu?: string;

  /**
   * 内存（G）
   */
  memory?: string;

  /**
   * 系统盘（G）
   */
  systemHardDisk?: string;

  /**
   * 数据盘（G）
   */
  dataHardDisk?: string;

  /**
   * 申请工单号
   */
  applyOrderNo?: string;

  /**
   * 开通端口信息
   */
  openPortInfo?: string;

  /**
   * 端口用途
   */
  openPortUse?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
