export interface ItsmCountSystemVo {
  totalSystems?: number;
  totalServers?: number;
  healthySystems: number;
  unhealthySystems: number;
  healthyServers: number;
  unhealthyServers: number;
  noIPv4Systems: number;
}

export interface DictMonitorDataVO extends BaseEntity {
  dictCode: string;
  dictLabel: string;
  dictValue: string;
  cssClass: string;
  listClass: ElTagType;
  dictSort: number;
  remark: string;
}

export interface ItsmWorkOrderTrendCountVo {
  total?: number; // 总数（可选属性）
  processing?: number; // 审核中的数量
  completed?: number; // 办结的数量
  completionRate?: number; // 办结率（百分比）
  alertDistribution?: AlertDistribution; // 告警分布情况
}

export interface AlertDistribution {
  emergencyNum?: number; // 紧急级别的告警数量
  principalNum?: number; // 主要级别的告警数量
  secondaryNum?: number; // 次要级别的告警数量
  warnNum?: number; // 警告级别的告警数量
}

// 添加 TimeRangeType 枚举定义
export enum TimeRangeType {
  TODAY = 'today',
  WEEK = 'week',
  MOON = 'moon'
}

export interface SeriesDataVo {
  time: string;
  value: string;
}

export interface Top10DataVO {
  legend: LegendVO;
  xaxis: XAxisVO;
  series: SeriesDataVo[];
}

export interface LegendVO {
  data: string[];
}

export interface XAxisVO {
  data: string[];
}

export interface WorkOrderTrendVo {
  usageRateXAxis: string[];
  normalSeries: SeriesDataVo[];
  emergencySeries: SeriesDataVo[];
  warnSeries: SeriesDataVo[];
  secondarySeries: SeriesDataVo[];
}

// 添加工单平均处理时间VO
export interface WorkOrderProcessTimeVO {
  legend: {
    data: string[];
  };
  xaxis: {
    data: string[];
  };
  series: Array<{
    name: string;
    data: string[];
  }>;
}
