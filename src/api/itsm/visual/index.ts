import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { SystemInfoQuery, SystemInfoVO } from '@/api/itsm/systemInfo/types';
import {
  DictMonitorDataVO,
  ItsmCountSystemVo,
  ItsmWorkOrderTrendCountVo,
  TimeRangeType,
  Top10DataVO,
  WorkOrderProcessTimeVO,
  WorkOrderTrendVo
} from '@/api/itsm/visual/types';
import { ServerInfoQuery, ServerInfoVO } from '@/api/itsm/serverInfo/types';
import { ProjectInfoVO } from '@/api/itsm/projectInfo/types';
import { WorkOrderManagerQuery, WorkOrderManagerVO } from '@/api/itsm/workOrderManager/types';
import { EmergencyPlanQuery, EmergencyPlanVO } from '@/api/itsm/emergencyPlan/types';
import { ItsmAllProjectInfoVo, ProjectUserQuery, ProjectUserVO } from '@/api/itsm/projectUser/types';
import { UserQuery, UserVO } from '@/api/system/user/types';
import { DeptVO } from '@/api/system/dept/types';
import { EmergencyPlanTypeQuery, EmergencyPlanTypeVO } from '@/api/itsm/emergencyPlanType/types';

// 根据字典类型查询字典数据信息
export function getMonitorDicts(dictType: string): AxiosPromise<DictMonitorDataVO[]> {
  return request({
    url: '/itsm/monitorData/type/' + dictType,
    method: 'get'
  });
}

/**
 * 查询项目信息详细
 * @param projectId
 */
export const getProjectInfo = (projectId: string | number): AxiosPromise<ProjectInfoVO> => {
  return request({
    url: '/itsm/monitorData/' + projectId,
    method: 'get'
  });
};

/**
 * 查询系统信息列表
 * @param query
 * @returns {*}
 */

export const listSystemInfo = (query?: SystemInfoQuery): AxiosPromise<SystemInfoVO[]> => {
  return request({
    url: '/itsm/monitorData/systemList',
    method: 'get',
    params: query
  });
};

/**
 * 查询项目人员信息列表
 * @param query
 * @returns {*}
 */

export const listProjectUserInfo = (query?: ProjectUserQuery): AxiosPromise<ProjectUserVO[]> => {
  return request({
    url: '/itsm/monitorData/projectUserList',
    method: 'get',
    params: query
  });
};

/**
 * 查询服务器信息列表
 * @param query
 * @returns {*}
 */

export const listServerInfo = (query?: ServerInfoQuery): AxiosPromise<ServerInfoVO[]> => {
  return request({
    url: '/itsm/monitorData/serverList',
    method: 'get',
    params: query
  });
};

/**
 * 获取系统总数，以及正常系统数，异常系统数
 */
export const countSystem = (): AxiosPromise<ItsmCountSystemVo> => {
  return request({
    url: '/itsm/monitorData/countSystem',
    method: 'get'
  });
};

/**
 * 工单总览，统计当前工单总数，处理中，办结数，办结率，告警等级饼状图
 * @param params 查询参数
 * @param params.systemId 系统ID（可选）- 按系统过滤
 * @param params.projectId 项目ID（可选）- 🔥新增按项目过滤
 * @returns ItsmWorkOrderTrendCountVo 工单统计数据
 *
 * 🔥新增功能：支持项目和系统维度的工单统计
 * 使用场景：
 * - 用户选择项目时传递projectId获取该项目的工单统计
 * - 系统轮播时传递systemId获取该系统的工单统计
 */
export const workOrderCountAndGrade = (params?: {
  systemId?: string | number;
  projectId?: string | number;
}): AxiosPromise<ItsmWorkOrderTrendCountVo> => {
  return request({
    url: '/itsm/monitorData/workOrderCountAndGrade',
    method: 'get',
    params
  });
};

/**
 * 查询工单管理信息列表
 * @param query
 * @returns {*}
 */
export const listWorkOrderManager = (query?: WorkOrderManagerQuery): AxiosPromise<WorkOrderManagerVO[]> => {
  return request({
    url: '/itsm/monitorData/wordOrderList',
    method: 'get',
    params: query
  });
};

/**
 * 查询应急预案信息列表
 * @param query
 * @returns {*}
 */
export const listPlanInfo = (query?: EmergencyPlanQuery): AxiosPromise<EmergencyPlanVO[]> => {
  return request({
    url: '/itsm/monitorData/planList',
    method: 'get',
    params: query
  });
};

/**
 * 查询应急预案类型列表
 * @param query
 * @returns {*}
 */

export const listEmergencyPlanType = (query?: EmergencyPlanTypeQuery): AxiosPromise<EmergencyPlanTypeVO[]> => {
  return request({
    url: '/itsm/monitorData/planTypeList',
    method: 'get',
    params: query
  });
};

/**
 * 获取普罗米修斯资源使用率
 * @param params 请求参数
 * @param params.id 服务器id
 * @param params.rangeType 查询时段（TimeRangeType 枚举）
 */
export const getPrometheusResourceUsage = (params: { id: string; rangeTypeStr: TimeRangeType }): AxiosPromise<any> => {
  return request({
    url: '/itsm/monitorData/getAllMonitor',
    method: 'get',
    params: params
  });
};

/**
 * 获取按系统统计的工单数量，近7天，或者今天，或者最近30天
 * @param range 查询时间范围，默认为today
 * @returns 返回系统工单统计数据的Map
 */
export const getWorkOrderCountBySystem = (range: string = 'moon'): AxiosPromise<Top10DataVO> => {
  return request({
    url: '/itsm/monitorData/workOrderCountBySystem',
    method: 'get',
    params: { range }
  });
};

/**
 * 获取告警/工单趋势数据
 * @param params 查询参数
 * @param params.systemId 系统ID（可选）- 按系统过滤
 * @param params.projectId 项目ID（可选）- 🔥新增按项目过滤
 * @returns WorkOrderTrendVo 工单趋势数据
 *
 * 🔥新增功能：支持项目维度的工单趋势查询
 * 使用场景：用户选择项目时传递projectId获取该项目的趋势数据
 */
export const getWorkOrderTrend = (params?: { systemId?: string | number; projectId?: string | number }): AxiosPromise<WorkOrderTrendVo> => {
  return request({
    url: '/itsm/monitorData/trend',
    method: 'get',
    params
  });
};

/**
 * 获取工单平均处理时间数据
 * @param params 查询参数
 * @param params.systemId 系统ID（可选）- 按系统过滤
 * @param params.projectId 项目ID（可选）- 🔥新增按项目过滤
 * @returns WorkOrderProcessTimeVO 工单平均处理时间数据
 *
 * 🔥新增功能：支持项目维度的工单处理时间统计
 * 使用场景：用户选择项目时传递projectId获取该项目的处理时间数据
 */
export const getWorkOrderAverageProcessTime = (params?: {
  systemId?: string | number;
  projectId?: string | number;
}): AxiosPromise<WorkOrderProcessTimeVO> => {
  return request({
    url: '/itsm/monitorData/workOrderAverageProcessTime',
    method: 'get',
    params
  });
};

//获取项目，以及所属系统，及其用户
export const getItsmAllProjectInfo = (): AxiosPromise<ItsmAllProjectInfoVo[]> => {
  return request({
    url: '/itsm/monitorData/getItsmAllProjectInfo',
    method: 'get'
  });
};

/**
 * 查询用户列表
 * @param query
 */
export const listUser = (query: UserQuery): AxiosPromise<UserVO[]> => {
  return request({
    url: '/itsm/monitorData/sysUserList',
    method: 'get',
    params: query
  });
};

/**
 * 查询部门下拉树结构
 */
export const deptTreeSelect = (): AxiosPromise<DeptVO[]> => {
  return request({
    url: '/itsm/monitorData/deptTree',
    method: 'get'
  });
};
