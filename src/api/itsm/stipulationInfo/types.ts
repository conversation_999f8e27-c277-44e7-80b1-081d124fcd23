export interface StipulationInfoVO {
  /**
   * 合同id
   */
  stipulationId: string | number;

  /**
   * 所属项目id
   */
  projectId: string | number;

  /**
   * 合同名称
   */
  contractName: string;

  /**
   * 合同编号
   */
  contractNumber: string;

  /**
   * 签约甲方
   */
  signFirstParty: string;

  /**
   * 签约乙方
   */
  signSecondParty: string;

  /**
   * 合同金额
   */
  amount: number;

  /**
   * 合同签订时间
   */
  signDate: string;

  /**
   * 合同交付内容
   */
  submitContent: string;

  /**
   * 合同款项支付比例
   */
  paymentRatio: string;

  /**
   * 合同付款信息
   */
  paymentInfo: string;

  /**
   * 合同付款登记
   */
  paymentRegister: string;

  /**
   * 附件id
   */
  fileId: string | number;
}

export interface StipulationInfoForm extends BaseEntity {
  /**
   * 合同id
   */
  stipulationId?: string | number;

  /**
   * 所属项目id
   */
  projectId?: string | number;

  /**
   * 合同名称
   */
  contractName?: string;

  /**
   * 合同编号
   */
  contractNumber?: string;

  /**
   * 签约甲方
   */
  signFirstParty?: string;

  /**
   * 签约乙方
   */
  signSecondParty?: string;

  /**
   * 合同金额
   */
  amount?: number;

  /**
   * 合同签订时间
   */
  signDate?: string;

  /**
   * 合同交付内容
   */
  submitContent?: string;

  /**
   * 合同款项支付比例
   */
  paymentRatio?: string;

  /**
   * 合同付款信息
   */
  paymentInfo?: string;

  /**
   * 合同付款登记
   */
  paymentRegister?: string;

  /**
   * 附件id
   */
  fileId?: string | number;
}

export interface StipulationInfoQuery extends PageQuery {
  /**
   * 所属项目id
   */
  projectId?: string | number;

  /**
   * 合同名称
   */
  contractName?: string;

  /**
   * 合同编号
   */
  contractNumber?: string;

  /**
   * 签约甲方
   */
  signFirstParty?: string;

  /**
   * 签约乙方
   */
  signSecondParty?: string;

  /**
   * 合同金额
   */
  amount?: number;

  /**
   * 合同签订时间
   */
  signDate?: string;

  /**
   * 合同交付内容
   */
  submitContent?: string;

  /**
   * 合同款项支付比例
   */
  paymentRatio?: string;

  /**
   * 合同付款信息
   */
  paymentInfo?: string;

  /**
   * 合同付款登记
   */
  paymentRegister?: string;

  /**
   * 附件id
   */
  fileId?: string | number;

  /**
   * 日期范围参数
   */
  params?: any;
}
