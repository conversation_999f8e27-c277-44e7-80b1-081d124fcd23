export interface ProjectInfoVO {
  /**
   * 项目id
   */
  projectId: string | number;

  /**
   * 项目名称
   */
  projectName: string;

  /**
   * 项目状态
   */
  projectStatus: string;

  /**
   * 建设责任部门
   */
  constructRespDept: string;

  /**
   * 部门负责人
   */
  deptManager: string;

  /**
   * 负责人电话
   */
  deptManagerPhone: string;

  /**
   * 工作联系人
   */
  workLinkman: string;

  /**
   * 联系人电话
   */
  workLinkmanPhone: string;

  /**
   * 计划验收时间
   */
  planAcceptanceDate: string;

  /**
   * 计划上线时间
   */
  planOnlineDate: string;

  /**
   * 项目总金额（元）
   */
  projectTotalAmount: string;

  /**
   * 信息安全投入（元）
   */
  infoSecurityInvest: string;

  /**
   * 项目内容描述
   */
  projectContent: string;
}

export interface ProjectInfoForm extends BaseEntity {
  /**
   * 项目id
   */
  projectId?: string | number;

  /**
   * 项目名称
   */
  projectName?: string;

  /**
   * 项目状态
   */
  projectStatus?: string;

  /**
   * 建设责任部门
   */
  constructRespDept?: string;

  /**
   * 部门负责人
   */
  deptManager?: string;

  /**
   * 负责人电话
   */
  deptManagerPhone?: string;

  /**
   * 工作联系人
   */
  workLinkman?: string;

  /**
   * 联系人电话
   */
  workLinkmanPhone?: string;

  /**
   * 计划验收时间
   */
  planAcceptanceDate?: string;

  /**
   * 计划上线时间
   */
  planOnlineDate?: string;

  /**
   * 项目总金额（元）
   */
  projectTotalAmount?: string;

  /**
   * 信息安全投入（元）
   */
  infoSecurityInvest?: string;

  /**
   * 项目内容描述
   */
  projectContent?: string;
}

export interface ProjectInfoQuery extends PageQuery {
  /**
   * 项目id
   */
  projectId?: string | number;

  /**
   * 项目名称
   */
  projectName?: string;

  /**
   * 项目状态
   */
  projectStatus?: string;

  /**
   * 建设责任部门
   */
  constructRespDept?: string;

  /**
   * 部门负责人
   */
  deptManager?: string;

  /**
   * 负责人电话
   */
  deptManagerPhone?: string;

  /**
   * 工作联系人
   */
  workLinkman?: string;

  /**
   * 联系人电话
   */
  workLinkmanPhone?: string;

  /**
   * 计划验收时间
   */
  planAcceptanceDate?: string;

  /**
   * 计划上线时间
   */
  planOnlineDate?: string;

  /**
   * 项目总金额（元）
   */
  projectTotalAmount?: string;

  /**
   * 信息安全投入（元）
   */
  infoSecurityInvest?: string;

  /**
   * 项目内容描述
   */
  projectContent?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
