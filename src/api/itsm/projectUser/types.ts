export interface ProjectUserVO {
  /**
   * 人员id
   */
  userId: string | number;

  userName: string;

  nickName: string;

  /**
   * 所属系统用户id
   */
  sysUserId: string | number;

  /**
   * 所属项目id
   */
  projectId: string | number;

  /**
   * 所属系统id
   */
  systemId: string | number;

  /**
   * 身份证号
   */
  idCard: string | number;

  /**
   * 是否在岗
   */
  atWorkFlag: string;

  /**
   * 服务开始时间
   */
  serviceBeginDate: string;

  /**
   * 用户所属公司名
   */
  userCompanyName: string;

  /**
   * 职位
   */
  position: string;

  /**
   * 政治面貌
   */
  politicsStatus: string;

  /**
   * 学历
   */
  eduBackground: string;

  /**
   * 学位
   */
  acaDegree: string;

  /**
   * 专业
   */
  major: string;

  /**
   * 毕业院校
   */
  gradSchool: string;

  /**
   * 联系电话
   */
  contactPhone: string;

  /**
   * 是否有配偶\直系血亲\三代以内旁系血亲\近姻亲在我单位工作情况
   */
  haveRelativesInUnit: string;

  /**
   * 紧急联系人及联系电话
   */
  emergencyInfo: string;

  /**
   * 是否开通vpn
   */
  vpnFlag: string;
}

export interface ItsmAllProjectInfoVo {
  /**
   * 项目id
   */
  projectId: string;

  /**
   * 项目名称
   */
  projectName: string;

  /**
   * 项目系统人员列表
   */
  userInfoVos?: ProjectSysUserInfoVo[];

  /**
   * 系统信息列表
   */
  systemInfoVos?: ItsmAllSystemInfoVo[];
}

export interface ItsmAllSystemInfoVo {
  /**
   * 系统id
   */
  systemId: string;

  /**
   * 所属项目id
   */
  projectId: string;

  /**
   * 系统名称
   */
  systemName: string;

  /**
   * 项目系统人员列表
   */
  userInfoVos?: ProjectSysUserInfoVo[];

  /**
   * 系统服务器信息列表列表
   */
  serverInfoVos?: ProjectSystemServerInfoVo[];
}

export interface ProjectSystemServerInfoVo {
  /**
   * 服务器id
   */
  serverId: string | number;

  /**
   * 所属系统id
   */
  systemId: string | number;

  /**
   * 服务器名
   */
  serverName: string;
}

export interface ProjectSysUserInfoVo {
  /**
   * 人员id
   */
  userId: string | number;

  /**
   * 用户名
   */
  userName: string;

  /**
   * 所属系统用户id
   */
  sysUserId: string | number;

  /**
   * 所属项目id
   */
  projectId: string | number;

  /**
   * 所属系统id
   */
  systemId: string | number;

  /**
   * 身份证号
   */
  idCard: string | number;

  /**
   * 是否在岗
   */
  atWorkFlag: string;

  /**
   * 服务开始时间
   */
  serviceBeginDate: string;

  /**
   * 用户所属公司名
   */
  userCompanyName: string;

  /**
   * 职位
   */
  position: string;

  /**
   * 政治面貌
   */
  politicsStatus: string;

  /**
   * 学历
   */
  eduBackground: string;

  /**
   * 学位
   */
  acaDegree: string;

  /**
   * 专业
   */
  major: string;

  /**
   * 毕业院校
   */
  gradSchool: string;

  /**
   * 联系电话
   */
  contactPhone: string;

  /**
   * 是否有配偶\直系血亲\三代以内旁系血亲\近姻亲在我单位工作情况
   */
  haveRelativesInUnit: string;

  /**
   * 紧急联系人及联系电话
   */
  emergencyInfo: string;

  /**
   * 是否开通vpn
   */
  vpnFlag: string;
}

export interface ProjectUserForm extends BaseEntity {
  /**
   * 人员id
   */
  userId?: string | number;

  /**
   * 所属系统用户id
   */
  sysUserId?: string | number;

  /**
   * 所属项目id
   */
  projectId?: string | number;

  /**
   * 所属系统id
   */
  systemId?: string | number;

  /**
   * 身份证号
   */
  idCard?: string | number;

  /**
   * 是否在岗
   */
  atWorkFlag?: string;

  /**
   * 服务开始时间
   */
  serviceBeginDate?: string;

  /**
   * 用户所属公司名
   */
  userCompanyName?: string;

  /**
   * 职位
   */
  position?: string;

  /**
   * 政治面貌
   */
  politicsStatus?: string;

  /**
   * 学历
   */
  eduBackground?: string;

  /**
   * 学位
   */
  acaDegree?: string;

  /**
   * 专业
   */
  major?: string;

  /**
   * 毕业院校
   */
  gradSchool?: string;

  /**
   * 联系电话
   */
  contactPhone?: string;

  /**
   * 是否有配偶\直系血亲\三代以内旁系血亲\近姻亲在我单位工作情况
   */
  haveRelativesInUnit?: string;

  /**
   * 紧急联系人及联系电话
   */
  emergencyInfo?: string;

  /**
   * 是否开通vpn
   */
  vpnFlag?: string;
}

export interface ProjectUserQuery extends PageQuery {
  /**
   * 所属系统用户id
   */
  sysUserId?: string | number;

  /**
   * 所属项目id
   */
  projectId?: string | number;

  /**
   * 所属系统id
   */
  systemId?: string | number;

  /**
   * 所属项目id列表
   */
  projectIds?: string[];

  /**
   * 所属系统id列表
   */
  systemIds?: string[];

  /**
   * 身份证号
   */
  idCard?: string | number;

  /**
   * 是否在岗
   */
  atWorkFlag?: string;

  /**
   * 服务开始时间
   */
  serviceBeginDate?: string;

  /**
   * 用户所属公司名
   */
  userCompanyName?: string;

  /**
   * 职位
   */
  position?: string;

  /**
   * 政治面貌
   */
  politicsStatus?: string;

  /**
   * 学历
   */
  eduBackground?: string;

  /**
   * 学位
   */
  acaDegree?: string;

  /**
   * 专业
   */
  major?: string;

  /**
   * 毕业院校
   */
  gradSchool?: string;

  /**
   * 联系电话
   */
  contactPhone?: string;

  /**
   * 是否有配偶\直系血亲\三代以内旁系血亲\近姻亲在我单位工作情况
   */
  haveRelativesInUnit?: string;

  /**
   * 紧急联系人及联系电话
   */
  emergencyInfo?: string;

  /**
   * 是否开通vpn
   */
  vpnFlag?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
