import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { ProjectUserVO, ProjectUserForm, ProjectUserQuery, ItsmAllProjectInfoVo } from '@/api/itsm/projectUser/types';

/**
 * 查询项目人员信息列表
 * @param query
 * @returns {*}
 */

export const listProjectUser = (query?: ProjectUserQuery): AxiosPromise<ProjectUserVO[]> => {
  return request({
    url: '/itsm/projectUser/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询项目人员信息详细
 * @param userId
 */
export const getProjectUser = (userId: string | number): AxiosPromise<ProjectUserVO> => {
  return request({
    url: '/itsm/projectUser/' + userId,
    method: 'get'
  });
};

/**
 * 新增项目人员信息
 * @param data
 */
export const addProjectUser = (data: ProjectUserForm) => {
  return request({
    url: '/itsm/projectUser',
    method: 'post',
    data: data
  });
};

/**
 * 修改项目人员信息
 * @param data
 */
export const updateProjectUser = (data: ProjectUserForm) => {
  return request({
    url: '/itsm/projectUser',
    method: 'put',
    data: data
  });
};

/**
 * 删除项目人员信息
 * @param userId
 */
export const delProjectUser = (userId: string | number | Array<string | number>) => {
  return request({
    url: '/itsm/projectUser/' + userId,
    method: 'delete'
  });
};

export const queryByProjectSysUser = (query?: ProjectUserQuery): AxiosPromise<ProjectUserVO[]> => {
  return request({
    url: '/itsm/projectUser/queryByProjectSysUser',
    method: 'get',
    params: query
  });
};

//获取项目，以及所属系统，及其用户
export const getItsmAllProjectInfo = (): AxiosPromise<ItsmAllProjectInfoVo[]> => {
  return request({
    url: '/itsm/projectUser/getItsmAllProjectInfo',
    method: 'get'
  });
};
