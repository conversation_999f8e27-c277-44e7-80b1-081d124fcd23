export interface EmergencyPlanVO {
  /**
   *
   */
  planId: string | number;

  /**
   * 状态，0-发布，1-草拟，2-意见征求，3-作废
   */
  status: string;

  /**
   * 应急预案类型
   */
  planTypeId: string | number;

  /**
   * 应急预案名称
   */
  name: string;

  /**
   * 经办人员
   */
  operator: string;

  /**
   * 拟稿日期
   */
  draftDate: string;

  /**
   * 征求意见日期
   */
  canvassDate: string;

  /**
   * 作废日期
   */
  obsoleteDate: string;

  /**
   * 发布日期
   */
  releaseDate: string;

  /**
   * 创建部门
   */
  createDeptStr?: string;
}

export interface EmergencyPlanForm extends BaseEntity {
  /**
   *
   */
  planId?: string | number;

  /**
   * 状态，0-发布，1-草拟，2-意见征求，3-作废
   */
  status?: string;

  /**
   * 应急预案类型
   */
  planTypeId?: string | number;

  /**
   * 应急预案名称
   */
  name?: string;

  /**
   * 经办人员
   */
  operator?: string;

  /**
   * 拟稿日期
   */
  draftDate?: string;

  /**
   * 征求意见日期
   */
  canvassDate?: string;

  /**
   * 作废日期
   */
  obsoleteDate?: string;

  /**
   * 发布日期
   */
  releaseDate?: string;

  createDept?: string | number;

  /**
   * 列表
   */
  planIds?: Array<string | number>;
}

export interface EmergencyPlanQuery extends PageQuery {
  /**
   * 状态，0-发布，1-草拟，2-意见征求，3-作废
   */
  status?: string;

  /**
   * 应急预案类型
   */
  planTypeId?: string | number;

  /**
   * 应急预案名称
   */
  name?: string;

  /**
   * 经办人员
   */
  operator?: string;

  /**
   * 拟稿日期
   */
  draftDate?: string;

  /**
   * 征求意见日期
   */
  canvassDate?: string;

  /**
   * 作废日期
   */
  obsoleteDate?: string;

  /**
   * 发布日期
   */
  releaseDate?: string;

  /**
   * 日期范围参数
   */
  params?: any;

  createDept?: string | number;
}
