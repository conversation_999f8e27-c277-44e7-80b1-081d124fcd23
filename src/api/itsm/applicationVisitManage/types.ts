export interface ApplicationVisitManageVO {
  /**
   * 资金id
   */
  appVisitId: string | number;

  /**
   * 所属项目id
   */
  projectId: string | number;

  /**
   * 所属系统id
   */
  systemId: string | number;

  /**
   * 唯一uuid，用于拼接给用户访问使用
   */
  onlyUuid?: string;

  /**
   * 模块名称
   */
  appName: string;

  applicationName: string;

  /**
   * 访问应用管理类型
   */
  appVisitUrlManageType?: string;
}

export interface ApplicationVisitManageForm extends BaseEntity {
  /**
   * 资金id
   */
  appVisitId?: string | number;

  /**
   * 所属项目id
   */
  projectId?: string | number;

  /**
   * 所属系统id
   */
  systemId?: string | number;

  /**
   * 唯一uuid，用于拼接给用户访问使用
   */
  onlyUuid?: string | number;

  /**
   * 模块名称
   */
  appName?: string;

  /**
   * 1-系统模块，2-系统所属模块
   */
  type?: string;
}

export interface ApplicationVisitManageQuery extends PageQuery {
  pageNum: number;
  pageSize: number;

  /**
   * 所属项目id
   */
  projectId?: string | number;

  /**
   * 所属系统id
   */
  systemId?: string | number;

  /**
   * 唯一uuid，用于拼接给用户访问使用
   */
  onlyUuid?: string | number;

  /**
   * 模块名称
   */
  appName?: string;

  /**
   * 日期范围参数
   */
  params?: any;

  /**
   * 用于前端向后端传递，从系统页面点击按钮进来还是直接访问菜单，1：系统页面，2：直接访问菜单
   */
  searchType?: string;

  /**
   * 1-系统模块，2-系统所属模块
   */
  type?: string;
}
