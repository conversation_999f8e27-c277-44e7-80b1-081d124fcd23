export interface SecureInfoVO {
  /**
   * 安全id
   */
  secureId: string | number;

  /**
   * 所属项目id
   */
  projectId: string | number;

  /**
   * 信息安全投入（元）
   */
  investFund: number;

  /**
   * 信息安全等保级别
   */
  secureLevel: string;

  /**
   * 是否完成等保测评
   */
  atInsuranceFlag: string;

  /**
   * 是否密码测评
   */
  pwdFlag: string;

  /**
   * 互联网地址
   */
  internet: string;

  /**
   * 是否对接物联网
   */
  iotFlag: string;

  /**
   * 是否对接视频共享平台
   */
  videoFlag: string | number;

  /**
   * 互联网接口地址
   */
  internetInterface: string;

  /**
   * 暴露互联网的端口
   */
  port: string;
}

export interface SecureInfoForm extends BaseEntity {
  /**
   * 安全id
   */
  secureId?: string | number;

  /**
   * 所属项目id
   */
  projectId?: string | number;

  /**
   * 所属系统id
   */
  systemId?: string | number;

  /**
   * 信息安全投入（元）
   */
  investFund?: number;

  /**
   * 信息安全等保级别
   */
  secureLevel?: string;

  /**
   * 是否完成等保测评
   */
  atInsuranceFlag?: string;

  /**
   * 是否密码测评
   */
  pwdFlag?: string;

  /**
   * 互联网地址
   */
  internet?: string;

  /**
   * 是否对接物联网
   */
  iotFlag?: string;

  /**
   * 是否对接视频共享平台
   */
  videoFlag?: string | number;

  /**
   * 互联网接口地址
   */
  internetInterface?: string;

  /**
   * 暴露互联网的端口
   */
  port?: string;
}

export interface SecureInfoQuery extends PageQuery {
  /**
   * 所属项目id
   */
  projectId?: string | number;

  /**
   * 所属项目id
   */
  systemId?: string | number;

  /**
   * 信息安全投入（元）
   */
  investFund?: number;

  /**
   * 信息安全等保级别
   */
  secureLevel?: string;

  /**
   * 是否完成等保测评
   */
  atInsuranceFlag?: string;

  /**
   * 是否密码测评
   */
  pwdFlag?: string;

  /**
   * 互联网地址
   */
  internet?: string;

  /**
   * 是否对接物联网
   */
  iotFlag?: string;

  /**
   * 是否对接视频共享平台
   */
  videoFlag?: string | number;

  /**
   * 互联网接口地址
   */
  internetInterface?: string;

  /**
   * 暴露互联网的端口
   */
  port?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
