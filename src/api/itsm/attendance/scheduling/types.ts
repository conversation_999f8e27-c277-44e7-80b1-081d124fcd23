export interface AttendanceSchedulingVO {
  /**
   * 排班id
   */
  schedulingId: string | number;

  /**
   * 考勤组
   */
  groupName: string;

  /**
   * 考勤开始时间
   */
  startTime: string;

  /**
   * 考勤结束时间
   */
  endTime: string;

  /**
   * 考勤组id列表
   */
  groupIds?: string[];

  /**
   * 考勤组人员姓名集合
   */
  groupNames?: string[];

  /**
   * 考勤人员姓名集合
   */
  attendancePeoPleNames?: string[];
}

export interface AttendanceSchedulingForm extends BaseEntity {
  /**
   * 排班id
   */
  schedulingId?: string | number;

  /**
   * 考勤组id
   */
  groupId?: string | number;

  /**
   * 考勤开始时间
   */
  startTimeStr?: string;

  /**
   * 考勤结束时间
   */
  endTimeStr?: string;

  /**
   * 考勤组id列表
   */
  groupIds?: string[];

  /**
   * 考勤时间区间集合
   */
  attendanceTimes?: string[];
}

export interface AttendanceSchedulingQuery extends PageQuery {
  /**
   * 考勤开始时间
   */
  startTimeStr?: string;

  /**
   * 考勤结束时间
   */
  endTimeStr?: string;

  /**
   * 考勤时间区间集合
   */
  attendanceTimes?: string[];

  /**
   * 日期范围参数
   */
  params?: any;
}
