import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import {
  AttendanceGropManageVO,
  AttendanceGropManageForm,
  AttendanceGropManageQuery,
  UserAttendanceGroupDetailVO
} from '@/api/itsm/attendance/gropManage/types';
import { AttendanceGroupDetailVO, ItsmAttendanceGropManageAddBo } from '@/api/itsm/attendance/gropManage/types';

/**
 * 查询考勤组列表
 * @param query
 * @returns {*}
 */

export const listAttendanceGropManage = (query?: AttendanceGropManageQuery): AxiosPromise<AttendanceGropManageVO[]> => {
  return request({
    url: '/itsm/attendanceGroupManage/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询考勤组详细
 * @param id
 */
export const getAttendanceGropManage = (id: string | number): AxiosPromise<AttendanceGropManageVO> => {
  return request({
    url: '/itsm/attendanceGroupManage/' + id,
    method: 'get'
  });
};

/**
 * 详情按钮展示详细信息
 * @param id
 */
export const getAttendanceGroupDetail = (id: string | number): AxiosPromise<AttendanceGroupDetailVO> => {
  return request({
    url: '/itsm/attendanceGroupManage/detail/' + id,
    method: 'get'
  });
};

// 获取用户考勤组详细信息
export const getUserAttendanceGroupDetail = (sysUserId: string | number): AxiosPromise<UserAttendanceGroupDetailVO> => {
  return request({
    url: '/itsm/attendanceGroupManage/userGroupInfo/' + sysUserId,
    method: 'get'
  });
};

/**
 * 新增考勤组
 * @param data
 */
export const addAttendanceGropManage = (data: AttendanceGropManageForm) => {
  return request({
    url: '/itsm/attendanceGroupManage',
    method: 'post',
    data: data
  });
};

/**
 * 新增考勤组重构
 * @param data
 */
export const addAttendanceGroup = (data: AttendanceGropManageForm): AxiosPromise<any> => {
  return request({
    url: '/itsm/attendanceGroupManage',
    method: 'post',
    data: data
  });
};

/**
 * 修改考勤组
 * @param data
 */
export const updateAttendanceGropManage = (data: AttendanceGropManageForm) => {
  return request({
    url: '/itsm/attendanceGroupManage',
    method: 'put',
    data: data
  });
};

/**
 * 删除考勤组
 * @param id
 */
export const delAttendanceGropManage = (id: string | number | Array<string | number>) => {
  return request({
    url: '/itsm/attendanceGroupManage/' + id,
    method: 'delete'
  });
};
