export interface AttendanceGropManageVO {
  /**
   * 主键
   */
  id: string | number;

  /**
   * 考勤组名称
   */
  groupName: string;

  /**
   * 考勤人数
   */
  peopleNum: string | number;

  /**
   * 考勤时间
   */
  attendanceTime: string | number;
}

export interface AttendanceGropManageForm extends BaseEntity {
  /**
   * 主键
   */
  id?: string | number;

  /**
   * 考勤组名称
   */
  groupName?: string;

  /**
   * 项目id
   */
  projectIds?: string[];

  /**
   * 系统id
   */
  systemIds?: string[];

  /**
   * 班次id
   */
  allocationIds?: string[];

  /**
   * 班次id
   */
  allocationId?: string | number;

  attendanceUsers?: string[];

  whitelistUsers?: string[];
}

export interface AttendanceGropManageQuery extends PageQuery {
  /**
   * 考勤组名称
   */
  groupName?: string;

  /**
   * 项目id
   */
  projectId?: string | number;

  /**
   * 系统id
   */
  systemId?: string | number;

  /**
   * 1-固定班制，2-排版制，3-自由工时
   */
  attendType?: string;

  /**
   * 班次id
   */
  allocationId?: string | number;

  /**
   * 日期范围参数
   */
  params?: any;
}

/**
 * 新增考勤组重构
 */
export interface ItsmAttendanceGropManageAddBo {
  groupName: string;
  projectIds: string[];
  systemIds: string[];
  allocationIds: string[];
  attendanceUsers: string[];
  whitelistUsers: string[];

  /**
   * 班次id
   */
  allocationId?: number;
}

/**
 * 点击详情按钮展示vo
 */
export interface AttendanceGroupDetailVO {
  groupName: string;
  projectIds: string[];
  projectNames: string[];
  systemIds: string[];
  systemNames: string[];
  allocationId: string;
  shiftName: string;
  attendanceUsers: string[];
  attendanceUserNames: string[];
  whitelistUsers: string[];
  whitelistUserNames: string[];
}

/**
 * 考勤统计点击用户名展示vo
 */
export interface UserAttendanceGroupDetailVO {
  id?: number;
  groupName?: string;
  projectNames?: string[];
  projectIds?: string[];
  systemNames?: string[];
  systemIds?: string[];
  allocationId?: number;
  shiftName?: string;
}
