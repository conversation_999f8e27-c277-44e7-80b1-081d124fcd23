import { SysMonitorVo } from '@/api/itsm/systemMonitor/types';
import request from '@/utils/request';
import { AxiosPromise } from 'axios';

/**
 * 查询项目->系统->服务器树结构
 */
export const sysMonitorTreeSelect = (): AxiosPromise<SysMonitorVo[]> => {
  return request({
    url: '/itsm/sysMonitor/sysMonitorTree',
    method: 'get'
  });
};

export const getAllMonitor = (id: string) => {
  return request({
    url: '/itsm/sysMonitor/getAllMonitor/' + id,
    method: 'get'
  });
};

export const getCpuMonitor = (id: string) => {
  return request({
    url: '/itsm/sysMonitor/getCpuMonitor/' + id,
    method: 'get'
  });
};

export const getMemoryMonitor = (id: string) => {
  return request({
    url: '/itsm/sysMonitor/getMemoryMonitor/' + id,
    method: 'get'
  });
};

export const getDiskMonitor = (id: string) => {
  return request({
    url: '/itsm/sysMonitor/getDiskMonitor/' + id,
    method: 'get'
  });
};

export default {
  sysMonitorTreeSelect,
  getCpuMonitor,
  getMemoryMonitor,
  getDiskMonitor,
  getAllMonitor
};
