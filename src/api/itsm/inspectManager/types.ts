export interface InspectManagerVO {
  /**
   * 巡检记录id
   */
  recordId: string | number;

  /**
   * 巡检编号
   */
  recordNo: string;

  /**
   * 巡检时间
   */
  inspectTime: string;

  /**
   * 所属项目id
   */
  projectId: string | number;

  /**
   * 所属系统id
   */
  systemId: string | number;

  /**
   * 巡检服务器IP
   */
  inspectIps: string;

  /**
   * 项目服务器总数
   */
  projectServerTotal: string;

  /**
   * 项目系统总数
   */
  projectSystemTotal: string;

  /**
   * 巡检服务器数量
   */
  inspectServerTotal: string;

  /**
   * 巡检系统数量
   */
  inspectSystemTotal: string;

  /**
   * 系统访问地址
   */
  systemVisitUrl: string;

  /**
   * 巡检人员
   */
  inpsectUserId: string | number;

  /**
   * 巡检人员电话
   */
  inspectUserPhone: string;

  /**
   * 审核人员
   */
  auditUserId: string | number;

  /**
   * 审核人员电话
   */
  auditUserPhone: string;

  /**
   * 服务器检查
   */
  serverCheckFlag: string;

  /**
   * 数据库检查
   */
  dbCheckFlag: string;

  /**
   * 中间件检查
   */
  middlewareCheckFlag: string | number;

  /**
   * 网络检查
   */
  networkCheckFlag: string;

  /**
   * 异常情况说明
   */
  description: string;
}

export interface InspectManagerForm extends BaseEntity {
  /**
   * 巡检记录id
   */
  recordId?: string | number;

  /**
   * 巡检编号
   */
  recordNo?: string;

  /**
   * 巡检时间
   */
  inspectTime?: string;

  /**
   * 所属项目id
   */
  projectId?: string | number;

  /**
   * 所属系统id
   */
  systemId?: string | number;

  /**
   * 巡检服务器IP
   */
  inspectIps?: string;

  /**
   * 项目服务器总数
   */
  projectServerTotal?: string;

  /**
   * 项目系统总数
   */
  projectSystemTotal?: string;

  /**
   * 巡检服务器数量
   */
  inspectServerTotal?: string;

  /**
   * 巡检系统数量
   */
  inspectSystemTotal?: string;

  /**
   * 系统访问地址
   */
  systemVisitUrl?: string;

  /**
   * 巡检人员
   */
  inpsectUserId?: string | number;

  /**
   * 巡检人员电话
   */
  inspectUserPhone?: string;

  /**
   * 审核人员
   */
  auditUserId?: string | number;

  /**
   * 审核人员电话
   */
  auditUserPhone?: string;

  /**
   * 服务器检查
   */
  serverCheckFlag?: string;

  /**
   * 数据库检查
   */
  dbCheckFlag?: string;

  /**
   * 中间件检查
   */
  middlewareCheckFlag?: string | number;

  /**
   * 网络检查
   */
  networkCheckFlag?: string;

  /**
   * 异常情况说明
   */
  description?: string;
}

export interface InspectManagerQuery extends PageQuery {
  /**
   * 巡检编号
   */
  recordNo?: string;

  /**
   * 巡检时间
   */
  inspectTime?: string;

  /**
   * 所属项目id
   */
  projectId?: string | number;

  /**
   * 巡检服务器IP
   */
  inspectIps?: string;

  /**
   * 项目服务器总数
   */
  projectServerTotal?: string;

  /**
   * 项目系统总数
   */
  projectSystemTotal?: string;

  /**
   * 巡检服务器数量
   */
  inspectServerTotal?: string;

  /**
   * 巡检系统数量
   */
  inspectSystemTotal?: string;

  /**
   * 系统访问地址
   */
  systemVisitUrl?: string;

  /**
   * 巡检人员
   */
  inpsectUserId?: string | number;

  /**
   * 巡检人员电话
   */
  inspectUserPhone?: string;

  /**
   * 审核人员
   */
  auditUserId?: string | number;

  /**
   * 审核人员电话
   */
  auditUserPhone?: string;

  /**
   * 服务器检查
   */
  serverCheckFlag?: string;

  /**
   * 数据库检查
   */
  dbCheckFlag?: string;

  /**
   * 中间件检查
   */
  middlewareCheckFlag?: string | number;

  /**
   * 网络检查
   */
  networkCheckFlag?: string;

  /**
   * 异常情况说明
   */
  description?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
