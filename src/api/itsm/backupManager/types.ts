export interface BackupManagerVO {
  /**
   * 备份id
   */
  backupId: string | number;

  /**
   * 编号
   */
  backupNo: string;

  /**
   * 所属项目id
   */
  projectId: string | number;

  systemId: string | number;

  /**
   * 巡检时间
   */
  inspectTime: string;

  /**
   * 备份类型
   */
  backupType: string;

  /**
   * 备份服务器信息
   */
  serverInfo: string;

  /**
   * 上次备案容量（GB）
   */
  preBackupCapacity: string;

  /**
   * 本次备份容量（GB）
   */
  backupCapacity: string;

  /**
   * 备份增长量（GB）
   */
  growCapacity: string;

  /**
   * 巡检人员
   */
  inpsectUserId: string | number;

  /**
   * 巡检人员电话
   */
  inspectUserPhone: string;

  /**
   * 审核人员
   */
  auditUserId: string | number;

  /**
   * 审核人员电话
   */
  auditUserPhone: string;

  /**
   * 情况说明
   */
  description: string;
}

export interface BackupManagerForm extends BaseEntity {
  /**
   * 备份id
   */
  backupId?: string | number;

  /**
   * 编号
   */
  backupNo?: string;

  /**
   * 所属项目id
   */
  projectId?: string | number;

  systemId: string | number;

  /**
   * 巡检时间
   */
  inspectTime?: string;

  /**
   * 备份类型
   */
  backupType?: string;

  /**
   * 备份服务器信息
   */
  serverInfo?: string;

  /**
   * 上次备案容量（GB）
   */
  preBackupCapacity?: string;

  /**
   * 本次备份容量（GB）
   */
  backupCapacity?: string;

  /**
   * 备份增长量（GB）
   */
  growCapacity?: string;

  /**
   * 巡检人员
   */
  inpsectUserId?: string | number;

  /**
   * 巡检人员电话
   */
  inspectUserPhone?: string;

  /**
   * 审核人员
   */
  auditUserId?: string | number;

  /**
   * 审核人员电话
   */
  auditUserPhone?: string;

  /**
   * 情况说明
   */
  description?: string;
}

export interface BackupManagerQuery extends PageQuery {
  /**
   * 编号
   */
  backupNo?: string;

  /**
   * 所属项目id
   */
  projectId?: string | number;

  systemId: string | number;

  /**
   * 巡检时间
   */
  inspectTime?: string;

  /**
   * 备份类型
   */
  backupType?: string;

  /**
   * 备份服务器信息
   */
  serverInfo?: string;

  /**
   * 上次备案容量（GB）
   */
  preBackupCapacity?: string;

  /**
   * 本次备份容量（GB）
   */
  backupCapacity?: string;

  /**
   * 备份增长量（GB）
   */
  growCapacity?: string;

  /**
   * 巡检人员
   */
  inpsectUserId?: string | number;

  /**
   * 巡检人员电话
   */
  inspectUserPhone?: string;

  /**
   * 审核人员
   */
  auditUserId?: string | number;

  /**
   * 审核人员电话
   */
  auditUserPhone?: string;

  /**
   * 情况说明
   */
  description?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
