export interface ServerApplicationLogVO {
  /**
   * 日志id
   */
  applicationLogId: string | number;

  /**
   * 备份id
   */
  applicationId: string | number;

  /**
   * 应用名称
   */
  applicationName: string;

  /**
   * 应用所在ip
   */
  applicationIp: string;

  /**
   * 应用端口
   */
  applicationPort: string;

  /**
   * 0-正常，1-异常
   */
  operation: string;

  /**
   * 服务器应用异常发生说明
   */
  remark: string;
}

export interface ServerApplicationLogForm extends BaseEntity {
  /**
   * 日志id
   */
  applicationLogId?: string | number;

  /**
   * 备份id
   */
  applicationId?: string | number;

  /**
   * 应用名称
   */
  applicationName?: string;

  /**
   * 应用所在ip
   */
  applicationIp?: string;

  /**
   * 应用端口
   */
  applicationPort?: string;

  /**
   * 0-正常，1-异常
   */
  operation?: string;

  /**
   * 服务器应用异常发生说明
   */
  remark?: string;
}

export interface ServerApplicationLogQuery extends PageQuery {
  /**
   * 备份id
   */
  applicationId?: string | number;

  /**
   * 应用名称
   */
  applicationName?: string;

  /**
   * 应用所在ip
   */
  applicationIp?: string;

  /**
   * 应用端口
   */
  applicationPort?: string;

  /**
   * 0-正常，1-异常
   */
  operation?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
