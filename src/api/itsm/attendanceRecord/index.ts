import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import {
  AttendanceRecordVO,
  AttendanceRecordForm,
  AttendanceRecordQuery,
  AttendanceCountVO,
  ItsmUserAttendanceGropInfoVo
} from '@/api/itsm/attendanceRecord/types';

/**
 * 查询考勤流水列表
 * @param query
 * @returns {*}
 */

export const listAttendanceRecord = (query?: AttendanceRecordQuery): AxiosPromise<AttendanceRecordVO[]> => {
  return request({
    url: '/itsm/attendanceRecord/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询考勤统计列表
 * @param query
 * @returns {*}
 */

export const listAttendanceCount = (query?: AttendanceRecordQuery): AxiosPromise<AttendanceCountVO[]> => {
  return request({
    url: '/itsm/attendanceCount/list',
    method: 'get',
    params: query
  });
};

/**
 * 根据当前行用户id查询考勤流水列表
 * @param query
 * @returns {*}
 */

export const listAttendanceBySysUserId = (query?: AttendanceRecordQuery): AxiosPromise<AttendanceRecordVO[]> => {
  return request({
    url: '/itsm/attendanceRecord/getUserRecordInfoByUserId',
    method: 'get',
    params: query
  });
};

/**
 * 查询考勤流水详细
 * @param recordId
 */
export const getAttendanceRecord = (recordId: string | number): AxiosPromise<AttendanceRecordVO> => {
  return request({
    url: '/itsm/attendanceRecord/' + recordId,
    method: 'get'
  });
};

/**
 * 新增考勤流水
 * @param data
 */
export const addAttendanceRecord = (data: AttendanceRecordForm) => {
  return request({
    url: '/itsm/attendanceRecord',
    method: 'post',
    data: data
  });
};

/**
 * 修改考勤流水
 * @param data
 */
export const updateAttendanceRecord = (data: AttendanceRecordForm) => {
  return request({
    url: '/itsm/attendanceRecord',
    method: 'put',
    data: data
  });
};

/**
 * 删除考勤流水
 * @param recordId
 */
export const delAttendanceRecord = (recordId: string | number | Array<string | number>) => {
  return request({
    url: '/itsm/attendanceRecord/' + recordId,
    method: 'delete'
  });
};
