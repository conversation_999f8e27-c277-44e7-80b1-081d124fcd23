export interface AttendanceRecordVO {
  /**
   * 考勤流水记录id
   */
  recordId: string | number;

  /**
   * 上班打卡经度
   */
  clockInLongitude: number;

  /**
   * 上班打卡维度
   */
  clockInLatitude: number;

  /**
   * 下班打卡经度
   */
  clockOutLongitude: number;

  /**
   * 下班打卡维度
   */
  clockOutLatitude: number;

  /**
   * 上班打卡时间
   */
  clockInTime: string;

  /**
   * 下班打卡时间
   */
  clockOutTime: string;

  /**
   * 打卡状态：1-正常，2-迟到，3-请假，4-外出，5-加班
   */
  status: string;

  /**
   * 打卡地点
   */
  locationName: string;

  /**
   * 打卡人
   */
  sysUserId: string | number;

  /**
   * 上班打卡状态，-2-早退，-1-迟到，0-未打卡，1-正常，2-已打卡，3-请假，4-外出
   */
  clockInStatus: string;

  /**
   * 下班打卡状态，-2-早退，-1-迟到，0-未打卡，1-正常，2-已打卡，3-请假，4-外出
   */
  clockOutStatus: string;
}

export interface AttendanceCountVO {
  /**
   * 上班时长
   */
  dutyDuration: number;

  /**
   * 加班时长
   */
  workOvertimeDuration: number;

  /**
   * 外出时长
   */
  goOutDuration: number;

  /**
   * 考勤人员姓名
   */
  userName: string;

  /**
   * 考勤人员id
   */
  sysUserId?: string | number;
}

export interface AttendanceRecordForm extends BaseEntity {
  /**
   * 考勤流水记录id
   */
  recordId?: string | number;

  /**
   * 上班打卡经度
   */
  clockInLongitude?: number;

  /**
   * 上班打卡维度
   */
  clockInLatitude?: number;

  /**
   * 下班打卡经度
   */
  clockOutLongitude?: number;

  /**
   * 下班打卡维度
   */
  clockOutLatitude?: number;

  /**
   * 上班打卡时间
   */
  clockInTime?: string;

  /**
   * 下班打卡时间
   */
  clockOutTime?: string;

  /**
   * 打卡状态：1-正常，2-迟到，3-请假，4-外出，5-加班
   */
  status?: string;

  /**
   * 打卡人
   */
  sysUserId?: string | number;
}

export interface AttendanceRecordQuery extends PageQuery {
  /**
   * 维度
   */
  latitude?: number;

  /**
   * 经度
   */
  longitude?: number;

  /**
   * 上班打卡时间
   */
  clockInTime?: string;

  /**
   * 下班打卡时间
   */
  clockOutTime?: string;

  /**
   * 打卡状态：1-正常，2-迟到，3-请假，4-外出，5-加班
   */
  status?: string;

  /**
   * 打卡人
   */
  sysUserId?: string | number;

  /**
   * 默认查询当天的数据
   */
  searchTime?: string;
  /**
   * 日期范围参数
   */
  params?: any;
}
