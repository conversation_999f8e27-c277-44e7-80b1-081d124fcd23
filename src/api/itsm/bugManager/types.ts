export interface BugManagerVO {
  /**
   * 漏洞id
   */
  bugId: string | number;

  /**
   * 编号
   */
  bugNo: string;

  /**
   * 所属项目id
   */
  projectId: string | number;

  /**
   * 负责人id
   */
  chargeUserId: string | number;

  /**
   * 联系电话
   */
  chargeUserPhone: string;

  /**
   * 发现时间
   */
  discoveryTime: string;

  /**
   * 系统名称
   */
  systemName: string;

  /**
   * 云主机IP
   */
  cloudServerIpv4: string;

  /**
   * 资产名称
   */
  assetName: string;

  /**
   * 个数
   */
  num: string;

  /**
   * 风险级别
   */
  riskLevel: string;

  /**
   * 漏洞分组
   */
  bugGroup: string;

  /**
   * 漏洞描述
   */
  description: string;

  /**
   * 解决办法
   */
  solution: string;

  /**
   * 处理状态
   */
  processingStatus: string;

  /**
   * 处理时间
   */
  processingTime: string;
}

export interface BugManagerForm extends BaseEntity {
  /**
   * 漏洞id
   */
  bugId?: string | number;

  /**
   * 编号
   */
  bugNo?: string;

  /**
   * 所属项目id
   */
  projectId?: string | number;

  /**
   * 负责人id
   */
  chargeUserId?: string | number;

  /**
   * 联系电话
   */
  chargeUserPhone?: string;

  /**
   * 发现时间
   */
  discoveryTime?: string;

  /**
   * 系统名称
   */
  systemName?: string;

  /**
   * 云主机IP
   */
  cloudServerIpv4?: string;

  /**
   * 资产名称
   */
  assetName?: string;

  /**
   * 个数
   */
  num?: string;

  /**
   * 风险级别
   */
  riskLevel?: string;

  /**
   * 漏洞分组
   */
  bugGroup?: string;

  /**
   * 漏洞描述
   */
  description?: string;

  /**
   * 解决办法
   */
  solution?: string;

  /**
   * 处理状态
   */
  processingStatus?: string;

  /**
   * 处理时间
   */
  processingTime?: string;
}

export interface BugManagerQuery extends PageQuery {
  /**
   * 编号
   */
  bugNo?: string;

  /**
   * 所属项目id
   */
  projectId?: string | number;

  /**
   * 负责人id
   */
  chargeUserId?: string | number;

  /**
   * 联系电话
   */
  chargeUserPhone?: string;

  /**
   * 发现时间
   */
  discoveryTime?: string;

  /**
   * 系统名称
   */
  systemName?: string;

  /**
   * 云主机IP
   */
  cloudServerIpv4?: string;

  /**
   * 资产名称
   */
  assetName?: string;

  /**
   * 个数
   */
  num?: string;

  /**
   * 风险级别
   */
  riskLevel?: string;

  /**
   * 漏洞分组
   */
  bugGroup?: string;

  /**
   * 漏洞描述
   */
  description?: string;

  /**
   * 解决办法
   */
  solution?: string;

  /**
   * 处理状态
   */
  processingStatus?: string;

  /**
   * 处理时间
   */
  processingTime?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
