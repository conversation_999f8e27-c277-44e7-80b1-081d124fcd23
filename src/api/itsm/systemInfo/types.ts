export interface SystemInfoVO {
  /**
   * 系统id
   */
  systemId: string | number;

  /**
   * 所属项目id
   */
  projectId: string | number;

  /**
   * 系统名称
   */
  systemName: string;

  /**
   * 系统状态
   */
  systemStatus: string;

  /**
   * 建设责任部门
   */
  constructRespDept: string;

  /**
   * 部门负责人
   */
  deptManager: string;

  /**
   * 负责人电话
   */
  deptManagerPhone: string;

  /**
   * 工作联系人
   */
  workLinkman: string;

  /**
   * 联系人电话
   */
  workLinkmanPhone: string;

  /**
   * 计划验收时间
   */
  planAcceptanceDate: string;

  /**
   * 计划上线时间
   */
  planOnlineDate: string;

  /**
   * 系统内容描述
   */
  systemContent: string;

  /**
   * 系统类型
   */
  systemType: string;

  /**
   * 系统架构
   */
  systemArchitecture: string;

  /**
   * 客户端类型
   */
  clientType: string;

  /**
   * 系统面向对象
   */
  systemObject: string;

  /**
   * 接入网络类型
   */
  networkType: string;

  /**
   * 是否完全国产化
   */
  localizationFlag: string;

  /**
   * 计划改造时间
   */
  planChangeDate: string;

  /**
   * 系统风险等级评估
   */
  systemRiskAssessment: string;

  /**
   * 是否属于政务网站或政务新媒体
   */
  govSystemFlag: string;

  /**
   * 特殊情况说明
   */
  specialExplanation: string;

  /**
   * 是否与省有应用/数据交互
   */
  proExchangeFlag: string;

  /**
   * 是否有移动端
   */
  mobileAppFlag: string;

  onlyUuid?: string;
}

export interface SystemInfoForm extends BaseEntity {
  /**
   * 系统id
   */
  systemId?: string | number;

  /**
   * 所属项目id
   */
  projectId?: string | number;

  /**
   * 系统名称
   */
  systemName?: string;

  /**
   * 系统状态
   */
  systemStatus?: string;

  /**
   * 建设责任部门
   */
  constructRespDept?: string;

  /**
   * 部门负责人
   */
  deptManager?: string;

  /**
   * 负责人电话
   */
  deptManagerPhone?: string;

  /**
   * 工作联系人
   */
  workLinkman?: string;

  /**
   * 联系人电话
   */
  workLinkmanPhone?: string;

  /**
   * 计划验收时间
   */
  planAcceptanceDate?: string;

  /**
   * 计划上线时间
   */
  planOnlineDate?: string;

  /**
   * 系统内容描述
   */
  systemContent?: string;

  /**
   * 系统类型
   */
  systemType?: string;

  /**
   * 系统架构
   */
  systemArchitecture?: string;

  /**
   * 客户端类型
   */
  clientType?: string;

  /**
   * 系统面向对象
   */
  systemObject?: string;

  /**
   * 接入网络类型
   */
  networkType?: string;

  /**
   * 是否完全国产化
   */
  localizationFlag?: string;

  /**
   * 计划改造时间
   */
  planChangeDate?: string;

  /**
   * 系统风险等级评估
   */
  systemRiskAssessment?: string;

  /**
   * 是否属于政务网站或政务新媒体
   */
  govSystemFlag?: string;

  /**
   * 特殊情况说明
   */
  specialExplanation?: string;

  /**
   * 是否与省有应用/数据交互
   */
  proExchangeFlag?: string;

  /**
   * 是否有移动端
   */
  mobileAppFlag?: string;
}

export interface SystemInfoQuery extends PageQuery {
  /**
   * 所属项目id
   */
  projectId?: string | number;

  /**
   * 系统名称
   */
  systemName?: string;

  /**
   * 系统状态
   */
  systemStatus?: string;

  /**
   * 建设责任部门
   */
  constructRespDept?: string;

  /**
   * 部门负责人
   */
  deptManager?: string;

  /**
   * 负责人电话
   */
  deptManagerPhone?: string;

  /**
   * 工作联系人
   */
  workLinkman?: string;

  /**
   * 联系人电话
   */
  workLinkmanPhone?: string;

  /**
   * 计划验收时间
   */
  planAcceptanceDate?: string;

  /**
   * 计划上线时间
   */
  planOnlineDate?: string;

  /**
   * 系统内容描述
   */
  systemContent?: string;

  /**
   * 系统类型
   */
  systemType?: string;

  /**
   * 系统架构
   */
  systemArchitecture?: string;

  /**
   * 客户端类型
   */
  clientType?: string;

  /**
   * 系统面向对象
   */
  systemObject?: string;

  /**
   * 接入网络类型
   */
  networkType?: string;

  /**
   * 是否完全国产化
   */
  localizationFlag?: string;

  /**
   * 计划改造时间
   */
  planChangeDate?: string;

  /**
   * 系统风险等级评估
   */
  systemRiskAssessment?: string;

  /**
   * 是否属于政务网站或政务新媒体
   */
  govSystemFlag?: string;

  /**
   * 特殊情况说明
   */
  specialExplanation?: string;

  /**
   * 是否与省有应用/数据交互
   */
  proExchangeFlag?: string;

  /**
   * 是否有移动端
   */
  mobileAppFlag?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}

export interface ApplicationVisitManageVO {
  // 现有字段...
  onlyUuid?: string;
  appVisitUrlManageType?: string;
  projectId: string | number;
  systemId: string | number;
  appName: string;
}
