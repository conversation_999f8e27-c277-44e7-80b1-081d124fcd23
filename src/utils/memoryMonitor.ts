// 🔥 新增：内存监控工具
// 用于监控页面内存使用情况，帮助诊断内存泄漏问题

interface MemoryInfo {
  used: number;
  total: number;
  limit: number;
  usagePercent: number;
  timestamp: number;
}

interface MemoryStats {
  current: MemoryInfo | null;
  history: MemoryInfo[];
  maxHistory: number;
  warnings: string[];
}

class MemoryMonitor {
  private stats: MemoryStats = {
    current: null,
    history: [],
    maxHistory: 100, // 保留最近100次记录
    warnings: []
  };

  private monitorTimer: number | null = null;
  private warningThreshold = 80; // 内存使用率警告阈值（百分比）
  private criticalThreshold = 90; // 内存使用率严重警告阈值（百分比）

  constructor() {
    this.updateMemoryInfo();
  }

  /**
   * 获取当前内存信息
   */
  private getMemoryInfo(): MemoryInfo | null {
    if (!performance.memory) {
      return null;
    }

    const memory = performance.memory;
    const used = Math.round(memory.usedJSHeapSize / 1024 / 1024);
    const total = Math.round(memory.totalJSHeapSize / 1024 / 1024);
    const limit = Math.round(memory.jsHeapSizeLimit / 1024 / 1024);
    const usagePercent = (used / limit) * 100;

    return {
      used,
      total,
      limit,
      usagePercent,
      timestamp: Date.now()
    };
  }

  /**
   * 更新内存信息
   */
  updateMemoryInfo(): MemoryInfo | null {
    const memInfo = this.getMemoryInfo();
    if (!memInfo) return null;

    this.stats.current = memInfo;
    this.stats.history.push(memInfo);

    // 保持历史记录在限制范围内
    if (this.stats.history.length > this.stats.maxHistory) {
      this.stats.history = this.stats.history.slice(-this.stats.maxHistory);
    }

    // 检查内存使用率警告
    this.checkMemoryWarnings(memInfo);

    return memInfo;
  }

  /**
   * 检查内存使用率警告
   */
  private checkMemoryWarnings(memInfo: MemoryInfo) {
    const { usagePercent, used, limit } = memInfo;
    const timestamp = new Date().toLocaleTimeString();

    if (usagePercent >= this.criticalThreshold) {
      const warning = `🚨 [${timestamp}] 严重警告：内存使用率 ${usagePercent.toFixed(1)}% (${used}MB/${limit}MB)`;
      this.stats.warnings.push(warning);
      console.error(warning);
    } else if (usagePercent >= this.warningThreshold) {
      const warning = `⚠️ [${timestamp}] 警告：内存使用率 ${usagePercent.toFixed(1)}% (${used}MB/${limit}MB)`;
      this.stats.warnings.push(warning);
      console.warn(warning);
    }

    // 保持警告记录在合理范围内
    if (this.stats.warnings.length > 50) {
      this.stats.warnings = this.stats.warnings.slice(-25);
    }
  }

  /**
   * 启动定期监控
   */
  startMonitoring(interval: number = 30000) {
    if (this.monitorTimer) {
      this.stopMonitoring();
    }

    console.log(`📊 启动内存监控，间隔: ${interval / 1000}秒`);
    this.monitorTimer = window.setInterval(() => {
      this.updateMemoryInfo();
    }, interval);
  }

  /**
   * 停止监控
   */
  stopMonitoring() {
    if (this.monitorTimer) {
      clearInterval(this.monitorTimer);
      this.monitorTimer = null;
      console.log('📊 内存监控已停止');
    }
  }

  /**
   * 获取内存统计信息
   */
  getStats(): MemoryStats {
    return { ...this.stats };
  }

  /**
   * 获取内存使用趋势
   */
  getMemoryTrend(minutes: number = 10): MemoryInfo[] {
    const cutoffTime = Date.now() - minutes * 60 * 1000;
    return this.stats.history.filter((info) => info.timestamp >= cutoffTime);
  }

  /**
   * 检查是否存在内存泄漏
   */
  checkMemoryLeak(): { hasLeak: boolean; trend: string; recommendation: string } {
    if (this.stats.history.length < 10) {
      return {
        hasLeak: false,
        trend: '数据不足',
        recommendation: '需要更多数据来分析内存趋势'
      };
    }

    const recent = this.stats.history.slice(-10);
    const older = this.stats.history.slice(-20, -10);

    if (older.length === 0) {
      return {
        hasLeak: false,
        trend: '数据不足',
        recommendation: '需要更多历史数据来分析内存趋势'
      };
    }

    const recentAvg = recent.reduce((sum, info) => sum + info.used, 0) / recent.length;
    const olderAvg = older.reduce((sum, info) => sum + info.used, 0) / older.length;
    const growthRate = ((recentAvg - olderAvg) / olderAvg) * 100;

    let hasLeak = false;
    let trend = '';
    let recommendation = '';

    if (growthRate > 10) {
      hasLeak = true;
      trend = `内存使用量持续增长 ${growthRate.toFixed(1)}%`;
      recommendation = '建议检查是否存在内存泄漏，清理不必要的数据和事件监听器';
    } else if (growthRate > 5) {
      trend = `内存使用量轻微增长 ${growthRate.toFixed(1)}%`;
      recommendation = '建议关注内存使用情况，定期清理数据';
    } else if (growthRate < -5) {
      trend = `内存使用量下降 ${Math.abs(growthRate).toFixed(1)}%`;
      recommendation = '内存使用情况良好';
    } else {
      trend = '内存使用量稳定';
      recommendation = '内存使用情况正常';
    }

    return { hasLeak, trend, recommendation };
  }

  /**
   * 生成内存报告
   */
  generateReport(): string {
    const stats = this.getStats();
    const leak = this.checkMemoryLeak();
    const trend = this.getMemoryTrend(10);

    let report = '📊 内存使用报告\n';
    report += '='.repeat(50) + '\n';

    if (stats.current) {
      report += `当前内存使用: ${stats.current.used}MB / ${stats.current.limit}MB (${stats.current.usagePercent.toFixed(1)}%)\n`;
    }

    report += `历史记录数量: ${stats.history.length}\n`;
    report += `警告数量: ${stats.warnings.length}\n`;
    report += `内存泄漏检查: ${leak.hasLeak ? '⚠️ 可能存在泄漏' : '✅ 正常'}\n`;
    report += `趋势分析: ${leak.trend}\n`;
    report += `建议: ${leak.recommendation}\n`;

    if (stats.warnings.length > 0) {
      report += '\n最近的警告:\n';
      report += stats.warnings.slice(-5).join('\n') + '\n';
    }

    if (trend.length > 0) {
      report += `\n最近10分钟内存趋势:\n`;
      trend.forEach((info, index) => {
        const time = new Date(info.timestamp).toLocaleTimeString();
        report += `${time}: ${info.used}MB (${info.usagePercent.toFixed(1)}%)\n`;
      });
    }

    return report;
  }

  /**
   * 清理历史数据
   */
  clearHistory() {
    this.stats.history = [];
    this.stats.warnings = [];
    console.log('🧹 内存监控历史数据已清理');
  }

  /**
   * 销毁监控器
   */
  destroy() {
    this.stopMonitoring();
    this.clearHistory();
    console.log('🔄 内存监控器已销毁');
  }
}

// 创建全局内存监控实例
export const memoryMonitor = new MemoryMonitor();

// 在开发环境下自动启动监控
if (process.env.NODE_ENV === 'development') {
  memoryMonitor.startMonitoring(30000); // 30秒间隔

  // 添加全局方法，方便在控制台调试
  (window as any).memoryMonitor = memoryMonitor;
  (window as any).getMemoryReport = () => {
    console.log(memoryMonitor.generateReport());
  };
}
