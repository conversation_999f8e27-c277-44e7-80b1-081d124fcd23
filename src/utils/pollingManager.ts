// src/utils/pollingManager.ts
class PollingManager {
  private timers: Map<string, number> = new Map();
  private isActive: boolean = true;
  private retryCount: Map<string, number> = new Map();
  private maxRetries = 3;
  private retryDelay = 5000;
  private lastUpdateTime: Map<string, number> = new Map();
  private minUpdateInterval = 1000;
  private executionCount: Map<string, number> = new Map(); // 🔥 新增：执行次数统计
  private maxExecutions = 1000; // 🔥 新增：最大执行次数限制
  private memoryCheckInterval = 100; // 🔥 新增：每100次执行检查一次内存

  constructor() {
    window.addEventListener('online', this.handleNetworkChange);
    window.addEventListener('offline', this.handleNetworkChange);

    // 🔥 新增：定期清理统计数据
    setInterval(() => {
      this.cleanupStats();
    }, 300000); // 5分钟清理一次
  }

  // 🔥 新增：清理统计数据
  private cleanupStats() {
    try {
      this.retryCount.clear();
      this.lastUpdateTime.clear();
      this.executionCount.clear();
      console.log('🧹 轮询管理器统计数据已清理');
    } catch (error) {
      console.error('❌ 轮询管理器统计数据清理失败:', error);
    }
  }

  private handleNetworkChange = () => {
    if (navigator.onLine) {
      console.log('🌐 网络连接恢复，轮询管理器恢复运行');
      this.resume();
    } else {
      console.log('🌐 网络连接断开，轮询管理器暂停运行');
      this.stopAll();
    }
  };

  startPolling(key: string, callback: () => Promise<void>, interval: number) {
    if (!this.isActive) return;
    this.stopPolling(key);

    // 🔥 新增：初始化执行计数
    this.executionCount.set(key, 0);

    const executeWithRetry = async () => {
      const now = Date.now();
      const lastUpdate = this.lastUpdateTime.get(key) || 0;
      if (now - lastUpdate < this.minUpdateInterval) {
        return;
      }

      // 🔥 新增：检查执行次数限制
      const currentExecutions = this.executionCount.get(key) || 0;
      if (currentExecutions >= this.maxExecutions) {
        console.warn(`⚠️ 轮询 ${key} 达到最大执行次数 ${this.maxExecutions}，重置计数器`);
        this.executionCount.set(key, 0);

        // 🔥 新增：强制垃圾回收（如果支持）
        if (window.gc) {
          window.gc();
        }
      }

      try {
        await callback();
        this.lastUpdateTime.set(key, now);
        this.retryCount.set(key, 0);

        // 🔥 新增：更新执行计数
        this.executionCount.set(key, currentExecutions + 1);

        // 🔥 新增：定期内存检查
        if (currentExecutions % this.memoryCheckInterval === 0) {
          this.checkMemoryUsage(key);
        }
      } catch (error) {
        console.error(`❌ 轮询 ${key} 执行失败:`, error);
        const currentRetries = this.retryCount.get(key) || 0;
        if (currentRetries < this.maxRetries) {
          this.retryCount.set(key, currentRetries + 1);
          console.log(`🔄 轮询 ${key} 重试 ${currentRetries + 1}/${this.maxRetries}`);
          setTimeout(executeWithRetry, this.retryDelay);
        } else {
          console.error(`❌ 轮询 ${key} 达到最大重试次数 ${this.maxRetries}`);
          this.retryCount.set(key, 0);

          // 🔥 新增：重试失败后暂停该轮询一段时间
          setTimeout(() => {
            console.log(`🔄 轮询 ${key} 重新启动`);
            this.retryCount.set(key, 0);
          }, this.retryDelay * 3); // 暂停3倍重试延迟时间
        }
      }
    };

    // 🔥 新增：包装执行函数，添加错误边界
    const safeExecute = async () => {
      try {
        await executeWithRetry();
      } catch (error) {
        console.error(`🚨 轮询 ${key} 发生未捕获错误:`, error);
      }
    };

    safeExecute().then();

    const timer = window.setInterval(() => {
      if (this.isActive && navigator.onLine) {
        safeExecute();
      }
    }, interval);

    this.timers.set(key, timer);
    console.log(`✅ 轮询 ${key} 已启动，间隔: ${interval}ms`);
  }

  // 🔥 新增：内存使用检查
  private checkMemoryUsage(key: string) {
    try {
      if (performance.memory) {
        const memInfo = performance.memory;
        const usedMB = Math.round(memInfo.usedJSHeapSize / 1024 / 1024);
        const totalMB = Math.round(memInfo.totalJSHeapSize / 1024 / 1024);
        const limitMB = Math.round(memInfo.jsHeapSizeLimit / 1024 / 1024);

        console.log(`📊 轮询 ${key} 内存使用: ${usedMB}MB / ${totalMB}MB (限制: ${limitMB}MB)`);

        // 🔥 新增：内存使用率过高时的警告
        const usagePercent = (usedMB / limitMB) * 100;
        if (usagePercent > 80) {
          console.warn(`⚠️ 内存使用率过高: ${usagePercent.toFixed(1)}%，建议清理内存`);
        }
      }
    } catch (error) {
      console.error(`❌ 内存检查失败:`, error);
    }
  }

  stopPolling(key: string) {
    const timer = this.timers.get(key);
    if (timer) {
      clearInterval(timer);
      this.timers.delete(key);
      console.log(`🛑 轮询 ${key} 已停止`);
    }

    // 🔥 新增：清理相关统计数据
    this.retryCount.delete(key);
    this.lastUpdateTime.delete(key);
    this.executionCount.delete(key);
  }

  stopAll() {
    console.log('🛑 停止所有轮询');
    this.isActive = false;
    this.timers.forEach((timer, key) => {
      clearInterval(timer);
      console.log(`🛑 轮询 ${key} 已停止`);
    });
    this.timers.clear();

    // 🔥 新增：清理所有统计数据
    this.cleanupStats();
  }

  resume() {
    console.log('▶️ 恢复轮询管理器');
    this.isActive = true;
  }

  // 🔥 新增：获取轮询状态信息
  getStatus() {
    return {
      isActive: this.isActive,
      activePollings: Array.from(this.timers.keys()),
      totalExecutions: Array.from(this.executionCount.entries()).reduce((sum, [, count]) => sum + count, 0),
      memoryInfo: performance.memory
        ? {
            used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
            total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024),
            limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024)
          }
        : null
    };
  }

  destroy() {
    console.log('🔄 销毁轮询管理器');
    window.removeEventListener('online', this.handleNetworkChange);
    window.removeEventListener('offline', this.handleNetworkChange);
    this.stopAll();

    // 🔥 新增：清理所有Map对象
    this.timers.clear();
    this.retryCount.clear();
    this.lastUpdateTime.clear();
    this.executionCount.clear();

    console.log('✅ 轮询管理器已销毁');
  }
}

export const pollingManager = new PollingManager();
