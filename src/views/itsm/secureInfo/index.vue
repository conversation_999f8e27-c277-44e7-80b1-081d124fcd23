<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="180px">
            <el-form-item label="所属系统" prop="systemId">
              <el-select v-model="queryParams.systemId" placeholder="请选择所属系统" clearable>
                <el-option v-for="dict in systemInfoList" :key="dict.systemId" :label="dict.systemName" :value="dict.systemId" />
              </el-select>
            </el-form-item>
            <el-form-item label="信息安全等保级别" prop="secureLevel">
              <el-select v-model="queryParams.secureLevel" placeholder="请选择信息安全等保级别" clearable>
                <el-option v-for="dict in itsm_secure_level" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="是否完成等保测评" prop="atInsuranceFlag">
              <el-select v-model="queryParams.atInsuranceFlag" placeholder="请选择是否开通vpn" clearable>
                <el-option v-for="dict in sys_yes_no" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="是否密码测评" prop="pwdFlag">
              <el-select v-model="queryParams.pwdFlag" placeholder="请选择是否开通vpn" clearable>
                <el-option v-for="dict in sys_yes_no" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="是否对接物联网" prop="iotFlag">
              <el-select v-model="queryParams.iotFlag" placeholder="请选择是否开通vpn" clearable>
                <el-option v-for="dict in sys_yes_no" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="是否对接视频共享平台" prop="videoFlag">
              <el-select v-model="queryParams.videoFlag" placeholder="请选择是否开通vpn" clearable>
                <el-option v-for="dict in sys_yes_no" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button v-hasPermi="['itsm:secureInfo:add']" type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['itsm:secureInfo:edit']" type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['itsm:secureInfo:remove']" type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['itsm:secureInfo:export']" type="warning" plain icon="Download" @click="handleExport">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @query-table="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="secureInfoList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="序号" align="center" fixed="left">
          <template #default="scope">
            <span>{{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}</span>
          </template>
        </el-table-column>
        <!--        <el-table-column label="所属项目" align="center" prop="projectId">-->
        <!--          <template #default="scope">-->
        <!--            <div v-for="project in projectInfoList">-->
        <!--              <span v-if="project.projectId === scope.row.projectId">{{ project.projectName }}</span>-->
        <!--            </div>-->
        <!--          </template>-->
        <!--        </el-table-column>-->
        <el-table-column label="所属系统" align="center" prop="systemId" width="200">
          <template #default="scope">
            <div v-for="system in systemInfoList">
              <span v-if="system.systemId === scope.row.systemId">{{ system.systemName }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="信息安全投入" align="center" prop="investFund" />
        <el-table-column label="信息安全等保级别" align="center" prop="secureLevel">
          <template #default="scope">
            <dict-tag :options="itsm_secure_level" :value="scope.row.secureLevel" />
          </template>
        </el-table-column>
        <el-table-column label="是否完成等保测评" align="center" prop="atInsuranceFlag">
          <template #default="scope">
            <dict-tag :options="sys_yes_no" :value="scope.row.atInsuranceFlag" />
          </template>
        </el-table-column>
        <el-table-column label="是否密码测评" align="center" prop="pwdFlag">
          <template #default="scope">
            <dict-tag :options="sys_yes_no" :value="scope.row.pwdFlag" />
          </template>
        </el-table-column>
        <el-table-column label="互联网地址" align="center" prop="internet" />
        <el-table-column label="是否对接物联网" align="center" prop="iotFlag">
          <template #default="scope">
            <dict-tag :options="sys_yes_no" :value="scope.row.atInsuranceFlag" />
          </template>
        </el-table-column>
        <el-table-column label="是否对接视频共享平台" align="center" prop="videoFlag">
          <template #default="scope">
            <dict-tag :options="sys_yes_no" :value="scope.row.videoFlag" />
          </template>
        </el-table-column>
        <el-table-column label="互联网接口地址" align="center" prop="internetInterface" />
        <el-table-column label="暴露互联网的端口" align="center" prop="port" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button v-hasPermi="['itsm:secureInfo:edit']" link type="primary" icon="Edit" @click="handleUpdate(scope.row)"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button v-hasPermi="['itsm:secureInfo:remove']" link type="primary" icon="Delete" @click="handleDelete(scope.row)"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" :total="total" @pagination="getList" />
    </el-card>

    <!-- 添加或修改安全信息对话框 -->
    <el-dialog v-model="dialog.visible" :title="dialog.title" width="500px" append-to-body>
      <el-form ref="secureInfoFormRef" :model="form" :rules="rules" label-width="180px">
        <el-form-item label="所属项目" prop="projectId">
          <el-select v-model="form.projectId" placeholder="请选择所属项目" clearable @change="handleProjectChange">
            <el-option v-for="dict in projectInfoList" :key="dict.projectId" :label="dict.projectName" :value="dict.projectId" />
          </el-select>
        </el-form-item>
        <el-form-item label="所属系统" prop="systemId">
          <el-select v-model="form.systemId" placeholder="请选择所属系统" clearable :loading="seachLoading">
            <el-option v-for="dict in selectSystemInfoList" :key="dict.systemId" :label="dict.systemName" :value="dict.systemId" />
          </el-select>
        </el-form-item>
        <el-form-item label="信息安全投入(元)" prop="investFund">
          <el-input v-model="form.investFund" placeholder="请输入信息安全投入" />
        </el-form-item>
        <el-form-item label="信息安全等保级别" prop="secureLevel">
          <el-select v-model="form.secureLevel" placeholder="请选择信息安全等保级别" clearable>
            <el-option v-for="dict in itsm_secure_level" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="是否完成等保测评" prop="atInsuranceFlag">
          <el-select v-model="form.atInsuranceFlag" placeholder="请选择是否完成等保测评">
            <el-option v-for="dict in sys_yes_no" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否密码测评" prop="pwdFlag">
          <el-select v-model="form.pwdFlag" placeholder="请选择是否密码测评">
            <el-option v-for="dict in sys_yes_no" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="互联网地址" prop="internet">
          <el-input v-model="form.internet" placeholder="请输入互联网地址" />
        </el-form-item>
        <el-form-item label="是否对接物联网" prop="iotFlag">
          <el-select v-model="form.iotFlag" placeholder="请选择是否对接物联网">
            <el-option v-for="dict in sys_yes_no" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否对接视频共享平台" prop="videoFlag">
          <el-select v-model="form.videoFlag" placeholder="请选择是否对接视频共享平台">
            <el-option v-for="dict in sys_yes_no" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="互联网接口地址" prop="internetInterface">
          <el-input v-model="form.internetInterface" placeholder="请输入互联网接口地址" />
        </el-form-item>
        <el-form-item label="暴露互联网的端口" prop="port">
          <el-input v-model="form.port" placeholder="请输入暴露互联网的端口" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="SecureInfo" lang="ts">
import { listSecureInfo, getSecureInfo, delSecureInfo, addSecureInfo, updateSecureInfo } from '@/api/itsm/secureInfo';
import { SecureInfoVO, SecureInfoQuery, SecureInfoForm } from '@/api/itsm/secureInfo/types';
import { ProjectInfoVO } from '@/api/itsm/projectInfo/types';
import { listProjectInfo } from '@/api/itsm/projectInfo';
import { SystemInfoVO } from '@/api/itsm/systemInfo/types';
import { listSystemInfo } from '@/api/itsm/systemInfo';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const { edu_background, sys_yes_no, politics_status, itsm_secure_level } = toRefs<any>(
  proxy?.useDict('edu_background', 'sys_yes_no', 'politics_status', 'itsm_secure_level')
);

const secureInfoList = ref<SecureInfoVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const secureInfoFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: SecureInfoForm = {
  secureId: undefined,
  projectId: undefined,
  systemId: undefined,
  investFund: undefined,
  secureLevel: undefined,
  atInsuranceFlag: undefined,
  pwdFlag: undefined,
  internet: undefined,
  iotFlag: undefined,
  videoFlag: undefined,
  internetInterface: undefined,
  port: undefined
};
const data = reactive<PageData<SecureInfoForm, SecureInfoQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    projectId: undefined,
    systemId: undefined,
    investFund: undefined,
    secureLevel: undefined,
    atInsuranceFlag: undefined,
    pwdFlag: undefined,
    internet: undefined,
    iotFlag: undefined,
    videoFlag: undefined,
    internetInterface: undefined,
    port: undefined,
    params: {}
  },
  rules: {
    secureId: [{ required: true, message: '安全id不能为空', trigger: 'blur' }],
    projectId: [{ required: true, message: '所属项目id不能为空', trigger: 'blur' }],
    systemId: [{ required: false, message: '所属系统id不能为空', trigger: 'blur' }],
    investFund: [{ required: true, message: '信息安全投入不能为空', trigger: 'blur' }],
    secureLevel: [{ required: true, message: '信息安全等保级别不能为空', trigger: 'blur' }],
    atInsuranceFlag: [{ required: true, message: '是否完成等保测评不能为空', trigger: 'blur' }],
    pwdFlag: [{ required: true, message: '是否密码测评不能为空', trigger: 'blur' }],
    internet: [{ required: true, message: '互联网地址不能为空', trigger: 'blur' }],
    iotFlag: [{ required: true, message: '是否对接物联网不能为空', trigger: 'blur' }],
    videoFlag: [{ required: true, message: '是否对接视频共享平台不能为空', trigger: 'blur' }],
    internetInterface: [{ required: true, message: '互联网接口地址不能为空', trigger: 'blur' }],
    port: [{ required: true, message: '暴露互联网的端口不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

// 项目信息
const projectInfoList = ref<ProjectInfoVO[]>([]);

const getProjectInfoList = async () => {
  const res = await listProjectInfo({});
  projectInfoList.value = res.rows;
};

const seachLoading = ref(true);
const selectSystemInfoList = ref<SystemInfoVO[]>([]);
const handleProjectChange = async (value: any) => {
  seachLoading.value = true;
  const res = await listSystemInfo({ projectId: value });
  selectSystemInfoList.value = res.rows;
  seachLoading.value = false;
};

/**
 * 系统信息列表
 */
const systemInfoList = ref<SystemInfoVO[]>([]);
const getSystemInfoList = async () => {
  const res = await listSystemInfo(queryParams.value);
  systemInfoList.value = res.rows;
};

/** 查询安全信息列表 */
const getList = async () => {
  loading.value = true;
  const res = await listSecureInfo(queryParams.value);
  secureInfoList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  secureInfoFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: SecureInfoVO[]) => {
  ids.value = selection.map((item) => item.secureId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加安全信息';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: SecureInfoVO) => {
  reset();
  const _secureId = row?.secureId || ids.value[0];
  const res = await getSecureInfo(_secureId);
  const systemRes = await listSystemInfo({ projectId: res.data.projectId });
  selectSystemInfoList.value = systemRes.rows;
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改安全信息';
};

/** 提交按钮 */
const submitForm = () => {
  secureInfoFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.secureId) {
        await updateSecureInfo(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addSecureInfo(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: SecureInfoVO) => {
  const _secureIds = row?.secureId || ids.value;
  await proxy?.$modal.confirm('是否确认删除安全信息编号为"' + _secureIds + '"的数据项？').finally(() => (loading.value = false));
  await delSecureInfo(_secureIds);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  const formattedDate = formatCurrentDate('YYYY-MM-DD HH:mm:ss');
  proxy?.download(
    'itsm/secureInfo/export',
    {
      ...queryParams.value
    },
    `安全信息_${formattedDate}.xlsx`
  );
};

// 格式化日期的函数
const formatCurrentDate = (format) => {
  const date = new Date();
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');

  if (format === 'YYYY-MM-DD HH:mm:ss') {
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  } else {
    throw new Error('Unsupported date format');
  }
};

onMounted(() => {
  getList();
  getProjectInfoList();
  getSystemInfoList();
});
</script>
