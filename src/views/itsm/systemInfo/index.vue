<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="230px">
            <el-form-item label="系统名称" prop="systemName">
              <el-input v-model="queryParams.systemName" placeholder="请输入系统名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="所属项目" prop="projectId">
              <el-select v-model="queryParams.projectId" placeholder="请选择所属项目" clearable>
                <el-option v-for="dict in allProjectInfoList" :key="dict.projectId" :label="dict.projectName" :value="dict.projectId" />
              </el-select>
            </el-form-item>
            <el-form-item label="系统状态" prop="systemStatus">
              <el-select v-model="queryParams.systemStatus" placeholder="请选择系统状态" clearable>
                <el-option v-for="dict in system_status" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="系统类型" prop="systemType">
              <el-select v-model="queryParams.systemType" placeholder="请选择系统类型" clearable>
                <el-option v-for="dict in system_type" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>

            <div v-if="showMore">
              <el-form-item label="系统架构" prop="systemArchitecture">
                <el-select v-model="queryParams.systemArchitecture" placeholder="请选择系统架构" clearable>
                  <el-option v-for="dict in system_architecture" :key="dict.value" :label="dict.label" :value="dict.value" />
                </el-select>
              </el-form-item>
              <el-form-item label="客户端类型" prop="clientType">
                <el-select v-model="queryParams.clientType" placeholder="请选择客户端类型" clearable>
                  <el-option v-for="dict in client_type" :key="dict.value" :label="dict.label" :value="dict.value" />
                </el-select>
              </el-form-item>
              <el-form-item label="系统面向对象" prop="systemObject">
                <el-select v-model="queryParams.systemObject" placeholder="请选择系统面向对象" clearable>
                  <el-option v-for="dict in system_object" :key="dict.value" :label="dict.label" :value="dict.value" />
                </el-select>
              </el-form-item>
              <el-form-item label="接入网络类型" prop="networkType">
                <el-select v-model="queryParams.networkType" placeholder="请选择接入网络类型" clearable>
                  <el-option v-for="dict in network_type" :key="dict.value" :label="dict.label" :value="dict.value" />
                </el-select>
              </el-form-item>
              <el-form-item label="是否完全国产化" prop="localizationFlag">
                <el-select v-model="queryParams.localizationFlag" placeholder="请选择是否完全国产化" clearable>
                  <el-option v-for="dict in sys_yes_no" :key="dict.value" :label="dict.label" :value="dict.value" />
                </el-select>
              </el-form-item>
              <el-form-item label="系统风险等级评估" prop="systemRiskAssessment">
                <el-select v-model="queryParams.systemRiskAssessment" placeholder="请选择系统风险等级评估" clearable>
                  <el-option v-for="dict in system_risk_assessment" :key="dict.value" :label="dict.label" :value="dict.value" />
                </el-select>
              </el-form-item>
              <el-form-item label="是否属于政务网站或政务新媒体" prop="govSystemFlag">
                <el-select v-model="queryParams.govSystemFlag" placeholder="请选择是否属于政务网站或政务新媒体" clearable>
                  <el-option v-for="dict in sys_yes_no" :key="dict.value" :label="dict.label" :value="dict.value" />
                </el-select>
              </el-form-item>
              <el-form-item label="是否与省有应用/数据交互" prop="proExchangeFlag">
                <el-select v-model="queryParams.proExchangeFlag" placeholder="请选择是否与省有应用/数据交互" clearable>
                  <el-option v-for="dict in sys_yes_no" :key="dict.value" :label="dict.label" :value="dict.value" />
                </el-select>
              </el-form-item>
              <el-form-item label="是否有移动端" prop="mobileAppFlag">
                <el-select v-model="queryParams.mobileAppFlag" placeholder="请选择是否有移动端" clearable>
                  <el-option v-for="dict in sys_yes_no" :key="dict.value" :label="dict.label" :value="dict.value" />
                </el-select>
              </el-form-item>
            </div>

            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
              <el-button type="text" @click="toggleMore">
                {{ showMore ? '收起搜索条件' : '更多搜索条件' }}
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button v-hasPermi="['itsm:systemInfo:add']" type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['itsm:systemInfo:edit']" type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['itsm:systemInfo:remove']" type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['itsm:systemInfo:export']" type="warning" plain icon="Download" @click="handleExport">导出</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['itsm:applicationVisitManage:list']" type="info" plain icon="Plus" @click="handleApplicationVisitManage"
              >访问应用管理</el-button
            >
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @query-table="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table ref="systemInfoTableRef" v-loading="loading" :data="systemInfoList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="序号" align="center" fixed="left" width="55">
          <template #default="scope">
            <span>{{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column label="系统名称" align="center" prop="systemName" fixed="left" width="300" />
        <el-table-column label="所属项目" align="center" prop="projectId" width="300">
          <template #default="scope">
            <div v-for="project in allProjectInfoList">
              <span v-if="project.projectId === scope.row.projectId">{{ project.projectName }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="系统状态" align="center" prop="systemStatus">
          <template #default="scope">
            <dict-tag :options="system_status" :value="scope.row.systemStatus" />
          </template>
        </el-table-column>
        <el-table-column label="建设责任部门" align="center" prop="constructRespDept" />
        <el-table-column label="部门负责人" align="center" prop="deptManager" />
        <el-table-column label="负责人电话" align="center" prop="deptManagerPhone" />
        <el-table-column label="工作联系人" align="center" prop="workLinkman" />
        <el-table-column label="联系人电话" align="center" prop="workLinkmanPhone" />
        <el-table-column label="计划验收时间" align="center" prop="planAcceptanceDate" width="120">
          <template #default="scope">
            <span>{{ parseTime(scope.row.planAcceptanceDate, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="计划上线时间" align="center" prop="planOnlineDate" width="120">
          <template #default="scope">
            <span>{{ parseTime(scope.row.planOnlineDate, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="系统内容描述" align="center" prop="systemContent" />
        <el-table-column label="系统类型" align="center" prop="systemType">
          <template #default="scope">
            <dict-tag :options="system_type" :value="scope.row.systemType" />
          </template>
        </el-table-column>
        <el-table-column label="系统架构" align="center" prop="systemArchitecture">
          <template #default="scope">
            <dict-tag :options="system_architecture" :value="scope.row.systemArchitecture" />
          </template>
        </el-table-column>
        <el-table-column label="客户端类型" align="center" prop="clientType">
          <template #default="scope">
            <dict-tag :options="client_type" :value="scope.row.clientType" />
          </template>
        </el-table-column>
        <el-table-column label="系统面向对象" align="center" prop="systemObject">
          <template #default="scope">
            <dict-tag :options="system_object" :value="scope.row.systemObject" />
          </template>
        </el-table-column>
        <el-table-column label="接入网络类型" align="center" prop="networkType">
          <template #default="scope">
            <dict-tag :options="network_type" :value="scope.row.networkType" />
          </template>
        </el-table-column>
        <el-table-column label="是否完全国产化" align="center" prop="localizationFlag">
          <template #default="scope">
            <dict-tag :options="sys_yes_no" :value="scope.row.localizationFlag" />
          </template>
        </el-table-column>
        <el-table-column label="计划改造时间" align="center" prop="planChangeDate" width="120">
          <template #default="scope">
            <span>{{ parseTime(scope.row.planChangeDate, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="系统风险等级评估" align="center" prop="systemRiskAssessment">
          <template #default="scope">
            <dict-tag :options="system_risk_assessment" :value="scope.row.systemRiskAssessment" />
          </template>
        </el-table-column>
        <el-table-column label="是否属于政务网站或政务新媒体" align="center" prop="govSystemFlag">
          <template #default="scope">
            <dict-tag :options="sys_yes_no" :value="scope.row.govSystemFlag" />
          </template>
        </el-table-column>
        <el-table-column label="特殊情况说明" align="center" prop="specialExplanation" width="300" />
        <el-table-column label="是否与省有应用/数据交互" align="center" prop="proExchangeFlag">
          <template #default="scope">
            <dict-tag :options="sys_yes_no" :value="scope.row.proExchangeFlag" />
          </template>
        </el-table-column>
        <el-table-column label="是否有移动端" align="center" prop="mobileAppFlag">
          <template #default="scope">
            <dict-tag :options="sys_yes_no" :value="scope.row.mobileAppFlag" />
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button v-hasPermi="['itsm:systemInfo:edit']" link type="primary" icon="Edit" @click="handleUpdate(scope.row)"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button v-hasPermi="['itsm:systemInfo:remove']" link type="danger" icon="Delete" @click="handleDelete(scope.row)"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" :total="total" @pagination="getList" />
    </el-card>

    <!-- 添加或修改系统信息对话框 -->
    <el-dialog v-model="dialog.visible" :title="dialog.title" width="800px" append-to-body>
      <el-form ref="systemInfoFormRef" :model="form" :rules="rules" label-width="250px">
        <el-form-item label="所属项目" prop="projectId">
          <el-select v-model="form.projectId" placeholder="请选择所属项目" clearable>
            <el-option v-for="dict in allProjectInfoList" :key="dict.projectId" :label="dict.projectName" :value="dict.projectId" />
          </el-select>
        </el-form-item>
        <el-form-item label="系统名称" prop="systemName">
          <el-input v-model="form.systemName" placeholder="请输入系统名称" />
        </el-form-item>
        <el-form-item label="系统状态" prop="systemStatus">
          <el-select v-model="form.systemStatus" placeholder="请选择系统状态" clearable>
            <el-option v-for="dict in system_status" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="建设责任部门" prop="constructRespDept">
          <el-input v-model="form.constructRespDept" placeholder="请输入建设责任部门" />
        </el-form-item>
        <el-form-item label="部门负责人" prop="deptManager">
          <el-input v-model="form.deptManager" placeholder="请输入部门负责人" />
        </el-form-item>
        <el-form-item label="负责人电话" prop="deptManagerPhone">
          <el-input v-model="form.deptManagerPhone" placeholder="请输入负责人电话" />
        </el-form-item>
        <el-form-item label="工作联系人" prop="workLinkman">
          <el-input v-model="form.workLinkman" placeholder="请输入工作联系人" />
        </el-form-item>
        <el-form-item label="联系人电话" prop="workLinkmanPhone">
          <el-input v-model="form.workLinkmanPhone" placeholder="请输入联系人电话" />
        </el-form-item>
        <el-form-item label="计划验收时间" prop="planAcceptanceDate">
          <el-date-picker v-model="form.planAcceptanceDate" clearable type="date" value-format="YYYY-MM-DD" placeholder="请选择计划验收时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="计划上线时间" prop="planOnlineDate">
          <el-date-picker v-model="form.planOnlineDate" clearable type="date" value-format="YYYY-MM-DD" placeholder="请选择计划上线时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="系统内容描述" prop="systemContent">
          <el-input v-model="form.systemContent" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="系统类型" prop="systemType">
          <el-select v-model="form.systemType" placeholder="请选择系统类型">
            <el-option v-for="dict in system_type" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="系统架构" prop="systemArchitecture">
          <el-select v-model="form.systemArchitecture" placeholder="请选择系统架构">
            <el-option v-for="dict in system_architecture" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="客户端类型" prop="clientType">
          <el-select v-model="form.clientType" placeholder="请选择客户端类型">
            <el-option v-for="dict in client_type" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="系统面向对象" prop="systemObject">
          <el-select v-model="form.systemObject" placeholder="请选择系统面向对象">
            <el-option v-for="dict in system_object" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="接入网络类型" prop="networkType">
          <el-select v-model="form.networkType" placeholder="请选择接入网络类型">
            <el-option v-for="dict in network_type" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否完全国产化" prop="localizationFlag">
          <el-select v-model="form.localizationFlag" placeholder="请选择是否完全国产化">
            <el-option v-for="dict in sys_yes_no" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="计划改造时间" prop="planChangeDate">
          <el-date-picker v-model="form.planChangeDate" clearable type="date" value-format="YYYY-MM-DD" placeholder="请选择计划改造时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="系统风险等级评估" prop="systemRiskAssessment">
          <el-select v-model="form.systemRiskAssessment" placeholder="请选择系统风险等级评估">
            <el-option v-for="dict in system_risk_assessment" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否属于政务网站或政务新媒体" prop="govSystemFlag">
          <el-select v-model="form.govSystemFlag" placeholder="请选择是否属于政务网站或政务新媒体">
            <el-option v-for="dict in sys_yes_no" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="特殊情况说明" prop="specialExplanation">
          <el-input v-model="form.specialExplanation" type="textarea" placeholder="请输入特殊情况说明" />
        </el-form-item>
        <el-form-item label="是否与省有应用/数据交互" prop="proExchangeFlag">
          <el-select v-model="form.proExchangeFlag" placeholder="请选择是否与省有应用/数据交互">
            <el-option v-for="dict in sys_yes_no" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否有移动端" prop="mobileAppFlag">
          <el-select v-model="form.mobileAppFlag" placeholder="请选择是否有移动端">
            <el-option v-for="dict in sys_yes_no" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 访问应用管理弹窗 -->
    <el-dialog
      v-model="applicationVisitManageDialog.visible"
      title="访问应用管理"
      width="80%"
      append-to-body
      @close="handleApplicationVisitManageDialogClose"
    >
      <!-- 只有在没有详情组件时，才显示原有内容 -->
      <template v-if="!applicationVisitManageDialog.showDetail">
        <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
          <div v-show="showSearch" class="mb-[10px]">
            <el-card shadow="hover">
              <el-form ref="queryFormRef" :model="queryAppManageParams" :inline="true">
                <el-form-item label="所属项目" prop="projectId">
                  <el-select
                    v-model="queryAppManageParams.projectId"
                    placeholder="请选择所属项目"
                    clearable
                    :loading="searchLoading"
                    @change="handleProjectChange"
                  >
                    <el-option v-for="dict in allProjectInfoList" :key="dict.projectId" :label="dict.projectName" :value="dict.projectId" />
                  </el-select>
                </el-form-item>
                <el-form-item label="所属系统" prop="systemId">
                  <el-select v-model="queryAppManageParams.systemId" placeholder="请选择所属系统" clearable :loading="searchLoading">
                    <el-option v-for="dict in selectItsmAllSystemInfoVoList" :key="dict.systemId" :label="dict.systemName" :value="dict.systemId" />
                  </el-select>
                </el-form-item>
                <el-form-item label="模块名称" prop="appName">
                  <el-input v-model="queryAppManageParams.appName" placeholder="请输入模块名称" clearable @keyup.enter="handleQuery" />
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" icon="Search" @click="handleAPPManageQuery">搜索</el-button>
                  <el-button icon="Refresh" @click="appManageResetQuery">重置</el-button>
                </el-form-item>
              </el-form>
            </el-card>
          </div>
        </transition>

        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button v-hasPermi="['itsm:applicationVisitManage:add']" type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['itsm:applicationVisitManage:edit']" type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              v-hasPermi="['itsm:applicationVisitManage:remove']"
              type="danger"
              plain
              icon="Delete"
              :disabled="multiple"
              @click="handleDelete()"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['itsm:applicationVisitManage:export']" type="warning" plain icon="Download" @click="handleExport">导出</el-button>
          </el-col>
        </el-row>

        <el-table v-loading="applicationVisitManageDialog.loading" :data="applicationVisitManageList">
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column label="序号" align="center" fixed="left" width="55">
            <template #default="scope">
              <span>{{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}</span>
            </template>
          </el-table-column>
          <el-table-column label="所属项目" align="center" prop="projectId" width="300">
            <template #default="scope">
              <div v-for="project in allProjectInfoList">
                <span v-if="project.projectId === scope.row.projectId">{{ project.projectName }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="所属系统" align="center" prop="systemId" width="200">
            <template #default="scope">
              <span>{{ getSystemName(scope.row.projectId, scope.row.systemId) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="模块名称" align="center" :show-overflow-tooltip="true">
            <template #default="scope">
              <el-link type="primary" @click="showDetailComponent(scope.row)">
                {{ scope.row.appName }}
              </el-link>
            </template>
          </el-table-column>
          <el-table-column label="所属类型" align="center" prop="type">
            <template #default="scope">
              <dict-tag :options="app_visit_type" :value="scope.row.type" />
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template #default="scope">
              <el-tooltip content="修改" placement="top">
                <el-button
                  v-hasPermi="['itsm:applicationVisitManage:edit']"
                  link
                  type="primary"
                  icon="Edit"
                  @click="handleUpdate(scope.row)"
                ></el-button>
              </el-tooltip>
              <el-tooltip content="删除" placement="top">
                <el-button
                  v-hasPermi="['itsm:applicationVisitManage:remove']"
                  link
                  type="primary"
                  icon="Delete"
                  @click="handleDelete(scope.row)"
                ></el-button>
              </el-tooltip>
              <el-tooltip content="复制" placement="top">
                <el-button link type="success" icon="CopyDocument" @click="handleAppManageCopy(scope.row)"></el-button>
              </el-tooltip>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="applicationVisitManageDialog.total > 0"
          v-model:page="applicationVisitManageDialog.pageNum"
          v-model:limit="applicationVisitManageDialog.pageSize"
          :total="applicationVisitManageDialog.total"
          @pagination="getApplicationVisitManageList"
        />
      </template>

      <!-- 详情组件区域 -->
      <template v-if="currentDetailComponent">
        <div class="detail-component-container">
          <el-button type="text" class="back-button" @click="closeDetailComponent">
            <el-icon><ArrowLeft /></el-icon> 返回
          </el-button>
          <component
            :is="currentDetailComponent"
            :system-id="applicationVisitManageDialog.systemId"
            :project-id="applicationVisitManageDialog.projectId"
            :current-row="currentDetailRow"
            @close="closeDetailComponent"
          />
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="SystemInfo" lang="ts">
import { addSystemInfo, delSystemInfo, getSystemInfo, listSystemInfo, updateSystemInfo } from '@/api/itsm/systemInfo';
import { SystemInfoForm, SystemInfoQuery, SystemInfoVO } from '@/api/itsm/systemInfo/types';
import { ProjectInfoVO } from '@/api/itsm/projectInfo/types';
import { listApplicationVisitManage } from '@/api/itsm/applicationVisitManage';
import { ApplicationVisitManageQuery, ApplicationVisitManageVO } from '@/api/itsm/applicationVisitManage/types';
import { ItsmAllProjectInfoVo, ItsmAllSystemInfoVo } from '@/api/itsm/projectUser/types';
import { getItsmAllProjectInfo } from '@/api/itsm/projectUser';
import { ElMessage } from 'element-plus';
import DictDataComponent from '@/views/itsm/systemInfo/data.vue';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const {
  network_type,
  system_risk_assessment,
  system_object,
  system_type,
  sys_yes_no,
  system_architecture,
  system_status,
  client_type,
  app_visit_url_manage,
  app_visit_type
} = toRefs<any>(
  proxy?.useDict(
    'network_type',
    'system_risk_assessment',
    'system_object',
    'system_type',
    'sys_yes_no',
    'system_architecture',
    'system_status',
    'client_type',
    'app_visit_url_manage',
    'app_visit_type'
  )
);

const systemInfoList = ref<SystemInfoVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
// 项目信息
const projectInfoList = ref<ProjectInfoVO[]>([]);

const queryFormRef = ref<ElFormInstance>();
const systemInfoFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

//访问应用管理相关
const applicationVisitManageList = ref<ApplicationVisitManageVO[]>([]);
const applicationVisitManageDialog = reactive({
  visible: false,
  loading: false,
  total: 0,
  pageNum: 1,
  pageSize: 10,
  projectId: undefined as string | number | undefined,
  systemId: undefined as string | number | undefined,
  type: undefined as string | undefined,
  showDetail: false // 新增属性
});

const currentDetailComponent = shallowRef<any>(null);
const currentDetailRow = ref<ApplicationVisitManageVO | null>(null);

const initFormData: SystemInfoForm = {
  systemId: undefined,
  projectId: undefined,
  systemName: undefined,
  systemStatus: undefined,
  constructRespDept: undefined,
  deptManager: undefined,
  deptManagerPhone: undefined,
  workLinkman: undefined,
  workLinkmanPhone: undefined,
  planAcceptanceDate: undefined,
  planOnlineDate: undefined,
  systemContent: undefined,
  systemType: undefined,
  systemArchitecture: undefined,
  clientType: undefined,
  systemObject: undefined,
  networkType: undefined,
  localizationFlag: undefined,
  planChangeDate: undefined,
  systemRiskAssessment: undefined,
  govSystemFlag: undefined,
  specialExplanation: undefined,
  proExchangeFlag: undefined,
  mobileAppFlag: undefined
};
const data = reactive<PageData<SystemInfoForm, SystemInfoQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    projectId: undefined,
    systemName: undefined,
    systemStatus: undefined,
    constructRespDept: undefined,
    deptManager: undefined,
    deptManagerPhone: undefined,
    workLinkman: undefined,
    workLinkmanPhone: undefined,
    planAcceptanceDate: undefined,
    planOnlineDate: undefined,
    systemContent: undefined,
    systemType: undefined,
    systemArchitecture: undefined,
    clientType: undefined,
    systemObject: undefined,
    networkType: undefined,
    localizationFlag: undefined,
    planChangeDate: undefined,
    systemRiskAssessment: undefined,
    govSystemFlag: undefined,
    specialExplanation: undefined,
    proExchangeFlag: undefined,
    mobileAppFlag: undefined,
    params: {}
  },
  rules: {
    systemId: [{ required: true, message: '系统id不能为空', trigger: 'blur' }],
    projectId: [{ required: true, message: '所属项目id不能为空', trigger: 'blur' }],
    systemName: [{ required: true, message: '系统名称不能为空', trigger: 'blur' }],
    systemStatus: [{ required: true, message: '系统状态不能为空', trigger: 'change' }],
    constructRespDept: [{ required: true, message: '建设责任部门不能为空', trigger: 'blur' }],
    deptManager: [{ required: true, message: '部门负责人不能为空', trigger: 'blur' }],
    deptManagerPhone: [{ required: true, message: '负责人电话不能为空', trigger: 'blur' }],
    workLinkman: [{ required: true, message: '工作联系人不能为空', trigger: 'blur' }],
    workLinkmanPhone: [{ required: true, message: '联系人电话不能为空', trigger: 'blur' }],
    planAcceptanceDate: [{ required: true, message: '计划验收时间不能为空', trigger: 'blur' }],
    planOnlineDate: [{ required: true, message: '计划上线时间不能为空', trigger: 'blur' }],
    systemContent: [{ required: true, message: '系统内容描述不能为空', trigger: 'blur' }],
    systemType: [{ required: true, message: '系统类型不能为空', trigger: 'change' }],
    systemArchitecture: [{ required: true, message: '系统架构不能为空', trigger: 'change' }],
    clientType: [{ required: true, message: '客户端类型不能为空', trigger: 'change' }],
    systemObject: [{ required: true, message: '系统面向对象不能为空', trigger: 'change' }],
    networkType: [{ required: true, message: '接入网络类型不能为空', trigger: 'change' }],
    localizationFlag: [{ required: true, message: '是否完全国产化不能为空', trigger: 'change' }],
    planChangeDate: [{ required: true, message: '计划改造时间不能为空', trigger: 'blur' }],
    systemRiskAssessment: [{ required: true, message: '系统风险等级评估不能为空', trigger: 'change' }],
    govSystemFlag: [{ required: true, message: '是否属于政务网站或政务新媒体不能为空', trigger: 'change' }],
    specialExplanation: [{ required: true, message: '特殊情况说明不能为空', trigger: 'blur' }],
    proExchangeFlag: [{ required: true, message: '是否与省有应用/数据交互不能为空', trigger: 'change' }],
    mobileAppFlag: [{ required: true, message: '是否有移动端不能为空', trigger: 'change' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询系统信息列表 */
const getList = async () => {
  loading.value = true;
  const res = await listSystemInfo(queryParams.value);
  systemInfoList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  systemInfoFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: SystemInfoVO[]) => {
  ids.value = selection.map((item) => item.systemId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加系统信息';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: SystemInfoVO) => {
  loading.value = true;
  reset();
  const _systemId = row?.systemId || ids.value[0];
  const res = await getSystemInfo(_systemId);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改系统信息';
  loading.value = false;
};

/** 提交按钮 */
const submitForm = () => {
  systemInfoFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.systemId) {
        await updateSystemInfo(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addSystemInfo(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: SystemInfoVO) => {
  const _systemIds = row?.systemId || ids.value;
  await proxy?.$modal.confirm('是否确认删除系统信息编号为"' + _systemIds + '"的数据项？').finally(() => (loading.value = false));
  await delSystemInfo(_systemIds);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'itsm/systemInfo/export',
    {
      ...queryParams.value
    },
    `系统信息_${new Date().getTime()}.xlsx`
  );
};

// 显示控制
const showMore = ref(false);
const toggleMore = () => {
  showMore.value = !showMore.value;
};

/** 访问应用管理按钮操作 */
const handleApplicationVisitManage = () => {
  // 重置详情组件状态
  resetDetailComponentState();

  // 检查是否选中数据
  if (ids.value.length === 0) {
    proxy?.$modal.msgError('请选择一条数据');
    return;
  }

  if (ids.value.length > 1) {
    proxy?.$modal.msgError('只能选择一条数据');
    return;
  }

  // 获取选中行的项目ID和系统ID
  const selectedRow = systemInfoList.value.find((row) => row.systemId === ids.value[0]);

  if (!selectedRow) {
    proxy?.$modal.msgError('未找到选中的数据');
    return;
  }

  applicationVisitManageDialog.projectId = selectedRow.projectId;
  applicationVisitManageDialog.systemId = selectedRow.systemId;
  applicationVisitManageDialog.type = '1';
  applicationVisitManageDialog.visible = true;
  applicationVisitManageDialog.pageNum = 1;

  // 获取访问应用管理列表
  getApplicationVisitManageList();
};

/** 获取访问应用管理列表 */
const getApplicationVisitManageList = async () => {
  applicationVisitManageDialog.loading = true;
  try {
    const query: ApplicationVisitManageQuery = {
      pageNum: applicationVisitManageDialog.pageNum,
      pageSize: applicationVisitManageDialog.pageSize,
      projectId: applicationVisitManageDialog.projectId,
      systemId: applicationVisitManageDialog.systemId,
      type: applicationVisitManageDialog.type,
      searchType: '1'
    };
    const res = await listApplicationVisitManage(query);
    applicationVisitManageList.value = res.rows;
    applicationVisitManageDialog.total = res.total;
  } catch (error) {
    proxy?.$modal.msgError('获取访问应用管理列表失败');
  } finally {
    applicationVisitManageDialog.loading = false;
  }
};

const queryAppManageParams = ref({
  pageNum: 1,
  pageSize: 10,
  projectId: undefined,
  systemId: undefined,
  appName: undefined,
  searchType: '1'
});

/** 搜索按钮操作(app访问应用管理) */
const handleAPPManageQuery = async () => {
  applicationVisitManageDialog.loading = true;
  try {
    const query: ApplicationVisitManageQuery = {
      pageNum: applicationVisitManageDialog.pageNum,
      pageSize: applicationVisitManageDialog.pageSize,
      projectId: queryAppManageParams.value.projectId,
      systemId: queryAppManageParams.value.systemId,
      appName: queryAppManageParams.value.appName,
      type: '1',
      searchType: '2'
    };
    const res = await listApplicationVisitManage(query);
    applicationVisitManageList.value = res.rows;
    applicationVisitManageDialog.total = res.total;
  } catch (error) {
    proxy?.$modal.msgError('查询访问应用管理失败');
    console.error(error);
  } finally {
    applicationVisitManageDialog.loading = false;
  }
};

/** 弹窗搜索app访问应用重置按钮操作 */
const appManageResetQuery = () => {
  queryFormRef.value?.resetFields();
  selectSystemInfoList.value = [];
  selectItsmAllSystemInfoVoList.value = [];
  handleAPPManageQuery();
};

const searchLoading = ref(true);
//项目，系统，人员
const allProjectInfoList = ref<ItsmAllProjectInfoVo[]>([]);
const getItsmAllProjectInfoVo = async () => {
  searchLoading.value = true;
  //这里查询一下项目，人员，以及所属人员
  const res = await getItsmAllProjectInfo();
  allProjectInfoList.value = res.data;
  searchLoading.value = false;
};

const selectSystemInfoList = ref<SystemInfoVO[]>([]);
const selectItsmAllSystemInfoVoList = ref<ItsmAllSystemInfoVo[]>([]);
const handleProjectChange = async (value: any) => {
  searchLoading.value = true;

  // 清空系统ID，防止旧值残留
  form.value.systemId = null;

  if (!value) {
    // 如果没有选择项目，则清空系统信息列表
    selectSystemInfoList.value = [];
    return;
  }

  // 找到所有与所选项目相关的系统信息
  const selectedProject = allProjectInfoList.value.find((project) => project.projectId === value);
  if (selectedProject && selectedProject.systemInfoVos) {
    // 更新系统信息列表
    selectItsmAllSystemInfoVoList.value = selectedProject.systemInfoVos;

    // 自动选择第一个系统（如果有）
    if (selectItsmAllSystemInfoVoList.value.length > 0) {
      form.value.systemId = selectItsmAllSystemInfoVoList.value[0].systemId;
    }
  } else {
    // 如果没有找到对应的项目或其系统信息，则清空系统信息列表
    selectItsmAllSystemInfoVoList.value = [];
  }
  searchLoading.value = false;
};

//表格的系统名渲染
const getSystemName = (projectId: string, systemId: string): string => {
  const project = allProjectInfoList.value.find((p) => p.projectId === projectId);
  if (project && project.systemInfoVos) {
    const system = project.systemInfoVos.find((s) => s.systemId === systemId);
    return system ? system.systemName : '';
  }
  return '';
};

// 新增复制方法
const handleCopy = (row: SystemInfoVO) => {
  // 获取系统状态字典
  const systemStatusDict = system_status.value.find((item) => item.value === row.systemStatus);

  // 构建复制的内容
  const copyContent = `${systemStatusDict?.label || ''}-${row.onlyUuid || ''}`;

  // 使用浏览器剪贴板API复制
  try {
    navigator.clipboard.writeText(copyContent).then(() => {
      ElMessage.success({
        message: copyContent,
        duration: 2000
      });
    });
  } catch (err) {
    ElMessage.error('复制失败');
    console.error('复制失败', err);
  }
};

// 新增复制方法（访问应用管理）
const handleAppManageCopy = (row: ApplicationVisitManageVO) => {
  // 直接获取第一个字典对象，并增加安全检查
  const appVisitUrlManageDict = app_visit_url_manage.value && app_visit_url_manage.value.length > 0 ? app_visit_url_manage.value[0] : null;
  console.log('appVisitUrlManageDict 打印结果：' + JSON.stringify(appVisitUrlManageDict, null, 4));

  // 构建复制的内容，增加更多的兜底逻辑
  // const copyContent = `${
  //   appVisitUrlManageDict?.value || row.appVisitUrlManageType || '默认URL'
  // }?webId=${row.systemId || '未知UUID'}&onlyUuid=${row.onlyUuid || '未知UUID'}`;

  const copyContent = `${appVisitUrlManageDict?.value || row.appVisitUrlManageType || 'xxx'}?webId=${row.systemId || '未知UUID'}&type=1`;

  // 使用浏览器剪贴板API复制
  try {
    navigator.clipboard.writeText(copyContent).then(() => {
      ElMessage.success({
        message: '复制成功：' + copyContent,
        duration: 2000
      });
    });
  } catch (err) {
    ElMessage.error('复制失败');
    console.error('复制失败', err);
  }
};

const showDetailComponent = (row: ApplicationVisitManageVO) => {
  // 只有在没有详情组件的时候才显示
  if (!applicationVisitManageDialog.showDetail) {
    // 显示详情组件，隐藏原有内容
    currentDetailComponent.value = DictDataComponent;
    applicationVisitManageDialog.showDetail = true;

    // 设置当前行的systemId和projectId
    applicationVisitManageDialog.systemId = row.systemId;
    applicationVisitManageDialog.projectId = row.projectId;

    // 新增：保存当前详情行的完整数据
    currentDetailRow.value = row;
  }
};

const closeDetailComponent = () => {
  // 隐藏详情组件，显示原有内容
  currentDetailComponent.value = null;
  applicationVisitManageDialog.showDetail = false;

  // 清空当前行数据
  currentDetailRow.value = null;
};

// 重置详情组件状态的方法
const resetDetailComponentState = () => {
  currentDetailComponent.value = null;
  applicationVisitManageDialog.showDetail = false;
  currentDetailRow.value = null;
};

// 修改弹窗关闭事件
const handleApplicationVisitManageDialogClose = () => {
  // 重置详情组件状态
  resetDetailComponentState();

  // 重置其他相关状态
  applicationVisitManageDialog.visible = false;
  applicationVisitManageDialog.projectId = undefined;
  applicationVisitManageDialog.systemId = undefined;

  // 清空选中的行
  ids.value = [];
  // 重置单选和多选状态
  single.value = true;
  multiple.value = true;

  // 清除表格选中状态
  systemInfoTableRef.value?.clearSelection();
};

// 添加 tableRef
const systemInfoTableRef = ref();

onMounted(() => {
  getList();
  getItsmAllProjectInfoVo();
});
</script>

<style scoped>
.detail-component-container {
  position: relative;
}

.back-button {
  position: absolute;
  top: -40px;
  left: 0;
  z-index: 10;
}
</style>
