<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="120px">
            <el-form-item label="编号" prop="bugNo">
              <el-input v-model="queryParams.bugNo" placeholder="请输入编号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="所属项目" prop="projectId">
              <el-select v-model="queryParams.projectId" placeholder="请选择所属项目" clearable>
                <el-option v-for="dict in projectInfoList" :key="dict.projectId" :label="dict.projectName" :value="dict.projectId" />
              </el-select>
            </el-form-item>
            <el-form-item label="发现时间" prop="discoveryTime">
              <el-date-picker v-model="queryParams.discoveryTime" clearable type="date" value-format="YYYY-MM-DD" placeholder="请选择发现时间" />
            </el-form-item>
            <el-form-item label="系统名称" prop="systemName">
              <el-input v-model="queryParams.systemName" placeholder="请输入系统名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="云主机IP" prop="cloudServerIpv4">
              <el-input v-model="queryParams.cloudServerIpv4" placeholder="请输入云主机IP" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="风险级别" prop="riskLevel">
              <el-input v-model="queryParams.riskLevel" placeholder="请输入风险级别" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="漏洞分组" prop="bugGroup">
              <el-input v-model="queryParams.bugGroup" placeholder="请输入漏洞分组" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="处理状态" prop="processingStatus">
              <el-select v-model="queryParams.processingStatus" placeholder="请选择处理状态" clearable>
                <el-option v-for="dict in handle_status" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="处理时间" prop="processingTime">
              <el-date-picker v-model="queryParams.processingTime" clearable type="date" value-format="YYYY-MM-DD" placeholder="请选择处理时间" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button v-hasPermi="['itsm:bugManager:add']" type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['itsm:bugManager:edit']" type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['itsm:bugManager:remove']" type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['itsm:bugManager:export']" type="warning" plain icon="Download" @click="handleExport">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @query-table="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="bugManagerList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="序号" align="center" fixed="left" width="55">
          <template #default="scope">
            <span>{{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column label="编号" align="center" prop="bugNo" fixed="left" width="120" />
        <el-table-column label="所属项目" align="center" prop="projectId" width="200">
          <template #default="scope">
            <div v-for="project in projectInfoList">
              <span v-if="project.projectId === scope.row.projectId">{{ project.projectName }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="所属系统" align="center" prop="systemId" width="200">
          <template #default="scope">
            <div v-for="system in systemInfoList">
              <span v-if="system.systemId === scope.row.systemId">{{ system.systemName }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="负责人" align="center" prop="chargeUserId">
          <template #default="scope">
            <div v-for="user in userList">
              <span v-if="user.userId === scope.row.chargeUserId">{{ user.nickName }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="联系电话" align="center" prop="chargeUserPhone" />
        <el-table-column label="发现时间" align="center" prop="discoveryTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.discoveryTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="系统名称" align="center" prop="systemName" />
        <el-table-column label="云主机IP" align="center" prop="cloudServerIpv4" />
        <el-table-column label="资产名称" align="center" prop="assetName" />
        <el-table-column label="个数" align="center" prop="num" />
        <el-table-column label="风险级别" align="center" prop="riskLevel" />
        <el-table-column label="漏洞分组" align="center" prop="bugGroup" />
        <el-table-column label="漏洞描述" align="center" prop="description" />
        <el-table-column label="解决办法" align="center" prop="solution" />
        <el-table-column label="处理状态" align="center" prop="processingStatus">
          <template #default="scope">
            <dict-tag :options="handle_status" :value="scope.row.processingStatus" />
          </template>
        </el-table-column>
        <el-table-column label="处理时间" align="center" prop="processingTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.processingTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button v-hasPermi="['itsm:bugManager:edit']" link type="primary" icon="Edit" @click="handleUpdate(scope.row)"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button v-hasPermi="['itsm:bugManager:remove']" link type="danger" icon="Delete" @click="handleDelete(scope.row)"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" :total="total" @pagination="getList" />
    </el-card>
    <!-- 添加或修改漏洞管理对话框 -->
    <el-dialog v-model="dialog.visible" :title="dialog.title" width="800px" append-to-body>
      <el-form ref="bugManagerFormRef" :model="form" :rules="rules" label-width="150px">
        <el-form-item label="编号" prop="bugNo">
          <el-input v-model="form.bugNo" placeholder="请输入编号" />
        </el-form-item>
        <el-form-item label="所属项目" prop="projectId">
          <el-select v-model="form.projectId" placeholder="请选择所属项目" clearable @change="handleProjectChange">
            <el-option v-for="dict in projectInfoList" :key="dict.projectId" :label="dict.projectName" :value="dict.projectId" />
          </el-select>
        </el-form-item>
        <el-form-item label="所属系统" prop="systemId">
          <!--          <el-input v-model="form.systemId" placeholder="请输入所属系统id" />-->
          <el-select v-model="form.systemId" placeholder="请选择所属系统" clearable :loading="seachLoading" @change="handleSystemChange">
            <el-option v-for="dict in selectSystemInfoList" :key="dict.systemId" :label="dict.systemName" :value="dict.systemId" />
          </el-select>
        </el-form-item>
        <el-form-item label="负责人" prop="chargeUserId">
          <el-select v-model="form.chargeUserId" placeholder="请选择负责人" clearable>
            <el-option v-for="user in projectUserList" :key="user.sysUserId" :label="user.nickName" :value="user.sysUserId" />
          </el-select>
        </el-form-item>
        <el-form-item label="联系电话" prop="chargeUserPhone">
          <el-input v-model="form.chargeUserPhone" placeholder="请输入联系电话" />
        </el-form-item>
        <el-form-item label="发现时间" prop="discoveryTime">
          <el-date-picker v-model="form.discoveryTime" clearable type="datetime" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择发现时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="系统名称" prop="systemName">
          <el-input v-model="form.systemName" placeholder="请输入系统名称" />
        </el-form-item>
        <el-form-item label="云主机IP" prop="cloudServerIpv4">
          <el-input v-model="form.cloudServerIpv4" placeholder="请输入云主机IP" />
        </el-form-item>
        <el-form-item label="资产名称" prop="assetName">
          <el-input v-model="form.assetName" placeholder="请输入资产名称" />
        </el-form-item>
        <el-form-item label="个数" prop="num">
          <el-input v-model="form.num" placeholder="请输入个数" />
        </el-form-item>
        <el-form-item label="风险级别" prop="riskLevel">
          <el-select v-model="form.riskLevel" placeholder="请选择风险级别" clearable>
            <el-option v-for="dict in system_risk_assessment" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="漏洞分组" prop="bugGroup">
          <el-input v-model="form.bugGroup" placeholder="请输入漏洞分组" />
        </el-form-item>
        <el-form-item label="漏洞描述" prop="description">
          <el-input v-model="form.description" type="textarea" placeholder="请输入漏洞描述" rows="5" />
        </el-form-item>
        <el-form-item label="解决办法" prop="solution">
          <el-input v-model="form.solution" type="textarea" placeholder="请输入解决办法" rows="5" />
        </el-form-item>
        <el-form-item label="处理状态" prop="processingStatus">
          <el-select v-model="form.processingStatus" placeholder="请选择处理状态">
            <el-option v-for="dict in handle_status" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="处理时间" prop="processingTime">
          <el-date-picker v-model="form.processingTime" clearable type="datetime" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择处理时间">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="BugManager" lang="ts">
import { listBugManager, getBugManager, delBugManager, addBugManager, updateBugManager } from '@/api/itsm/bugManager';
import { BugManagerVO, BugManagerQuery, BugManagerForm } from '@/api/itsm/bugManager/types';
import { ProjectInfoVO } from '@/api/itsm/projectInfo/types';
import { listProjectInfo } from '@/api/itsm/projectInfo';
import { ProjectUserVO } from '@/api/itsm/projectUser/types';
import { queryByProjectSysUser } from '@/api/itsm/projectUser';
import { SystemInfoVO } from '@/api/itsm/systemInfo/types';
import { listSystemInfo } from '@/api/itsm/systemInfo';
import { UserVO } from '@/api/system/user/types';
import { listUser } from '@/api/system/user';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { handle_status, system_risk_assessment } = toRefs<any>(proxy?.useDict('handle_status', 'system_risk_assessment'));

const bugManagerList = ref<BugManagerVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const bugManagerFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: BugManagerForm = {
  bugId: undefined,
  bugNo: undefined,
  projectId: undefined,
  chargeUserId: undefined,
  chargeUserPhone: undefined,
  discoveryTime: undefined,
  systemName: undefined,
  cloudServerIpv4: undefined,
  assetName: undefined,
  num: undefined,
  riskLevel: undefined,
  bugGroup: undefined,
  description: undefined,
  solution: undefined,
  processingStatus: undefined,
  processingTime: undefined
};
const data = reactive<PageData<BugManagerForm, BugManagerQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    bugNo: undefined,
    projectId: undefined,
    chargeUserId: undefined,
    chargeUserPhone: undefined,
    discoveryTime: undefined,
    systemName: undefined,
    cloudServerIpv4: undefined,
    assetName: undefined,
    num: undefined,
    riskLevel: undefined,
    bugGroup: undefined,
    description: undefined,
    solution: undefined,
    processingStatus: undefined,
    processingTime: undefined,
    params: {}
  },
  rules: {
    bugId: [{ required: true, message: '漏洞id不能为空', trigger: 'blur' }],
    projectId: [{ required: true, message: '所属项目id不能为空', trigger: 'blur' }],
    chargeUserId: [{ required: true, message: '负责人id不能为空', trigger: 'blur' }],
    discoveryTime: [{ required: true, message: '发现时间不能为空', trigger: 'blur' }],
    systemName: [{ required: true, message: '系统名称不能为空', trigger: 'blur' }],
    cloudServerIpv4: [{ required: true, message: '云主机IP不能为空', trigger: 'blur' }],
    num: [{ required: true, message: '个数不能为空', trigger: 'blur' }],
    riskLevel: [{ required: true, message: '风险级别不能为空', trigger: 'blur' }],
    bugGroup: [{ required: true, message: '漏洞分组不能为空', trigger: 'blur' }],
    description: [{ required: true, message: '漏洞描述不能为空', trigger: 'blur' }],
    solution: [{ required: true, message: '解决办法不能为空', trigger: 'blur' }],
    processingStatus: [{ required: true, message: '处理状态不能为空', trigger: 'change' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询漏洞管理列表 */
const getList = async () => {
  loading.value = true;
  const res = await listBugManager(queryParams.value);
  bugManagerList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  bugManagerFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: BugManagerVO[]) => {
  ids.value = selection.map((item) => item.bugId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加漏洞管理';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: BugManagerVO) => {
  loading.value = true;
  reset();
  const _bugId = row?.bugId || ids.value[0];
  const res = await getBugManager(_bugId);
  Object.assign(form.value, res.data);

  // 获取项目下的系统
  await handleProjectChange(form.value.projectId);
  await handleSystemChange(form.value.systemId);

  dialog.visible = true;
  dialog.title = '修改漏洞管理';
  loading.value = false;
};

/** 提交按钮 */
const submitForm = () => {
  bugManagerFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.bugId) {
        await updateBugManager(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addBugManager(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: BugManagerVO) => {
  const _bugIds = row?.bugId || ids.value;
  await proxy?.$modal.confirm('是否确认删除漏洞管理编号为"' + _bugIds + '"的数据项？').finally(() => (loading.value = false));
  await delBugManager(_bugIds);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'itsm/bugManager/export',
    {
      ...queryParams.value
    },
    `bugManager_${new Date().getTime()}.xlsx`
  );
};

/**
 * 项目信息
 */
const projectInfoList = ref<ProjectInfoVO[]>([]);
const getProjectInfoList = async () => {
  const res = await listProjectInfo({});
  projectInfoList.value = res.rows;
};

/**
 * 系统信息
 */
const systemInfoList = ref<SystemInfoVO[]>([]);
const getSystemInfoList = async () => {
  const res = await listSystemInfo({});
  systemInfoList.value = res.rows;
};

/**
 * 选择项目后
 */
const seachLoading = ref(true);
const selectSystemInfoList = ref<SystemInfoVO[]>([]);
const handleProjectChange = async (value: any) => {
  seachLoading.value = true;
  const res = await listSystemInfo({ projectId: value });
  selectSystemInfoList.value = res.rows;
  seachLoading.value = false;
};

/**
 * 选择系统后，查询对系统下的人员
 */
const projectUserList = ref<ProjectUserVO[]>();
const handleSystemChange = async (value: any) => {
  const res = await queryByProjectSysUser({ projectId: form.value.projectId, systemId: value });
  projectUserList.value = res.data;
};

/**
 * 用户信息
 */
const userList = ref<UserVO[]>();
const getUserList = async () => {
  const res = await listUser();
  userList.value = res.rows;
};

onMounted(() => {
  getList();
  getProjectInfoList();
  getSystemInfoList();
  // handleProjectChange();
  getUserList();
});
</script>
