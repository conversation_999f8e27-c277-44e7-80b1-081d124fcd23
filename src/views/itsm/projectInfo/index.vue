<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="120px">
            <el-form-item label="项目名称" prop="projectName">
              <el-input v-model="queryParams.projectName" placeholder="请输入项目名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="项目状态" prop="projectStatus">
              <el-select v-model="queryParams.projectStatus" placeholder="请选择项目状态" clearable>
                <el-option v-for="dict in project_status" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>

            <div v-if="showMore">
              <el-form-item label="建设责任部门" prop="constructRespDept">
                <el-input v-model="queryParams.constructRespDept" placeholder="请输入建设责任部门" clearable @keyup.enter="handleQuery" />
              </el-form-item>
              <el-form-item label="部门负责人" prop="deptManager">
                <el-input v-model="queryParams.deptManager" placeholder="请输入部门负责人" clearable @keyup.enter="handleQuery" />
              </el-form-item>
              <el-form-item label="负责人电话" prop="deptManagerPhone">
                <el-input v-model="queryParams.deptManagerPhone" placeholder="请输入负责人电话" clearable @keyup.enter="handleQuery" />
              </el-form-item>
              <el-form-item label="工作联系人" prop="workLinkman">
                <el-input v-model="queryParams.workLinkman" placeholder="请输入工作联系人" clearable @keyup.enter="handleQuery" />
              </el-form-item>
              <el-form-item label="联系人电话" prop="workLinkmanPhone">
                <el-input v-model="queryParams.workLinkmanPhone" placeholder="请输入联系人电话" clearable @keyup.enter="handleQuery" />
              </el-form-item>
              <el-form-item label="计划验收时间" prop="planAcceptanceDate">
                <el-date-picker
                  v-model="queryParams.planAcceptanceDate"
                  clearable
                  type="date"
                  value-format="YYYY-MM-DD"
                  placeholder="请选择计划验收时间"
                />
              </el-form-item>
              <el-form-item label="计划上线时间" prop="planOnlineDate">
                <el-date-picker
                  v-model="queryParams.planOnlineDate"
                  clearable
                  type="date"
                  value-format="YYYY-MM-DD"
                  placeholder="请选择计划上线时间"
                />
              </el-form-item>
              <el-form-item label="项目总金额" prop="projectTotalAmount">
                <el-input v-model="queryParams.projectTotalAmount" placeholder="请输入项目总金额" clearable @keyup.enter="handleQuery" />
              </el-form-item>
              <el-form-item label="信息安全投入" prop="infoSecurityInvest">
                <el-input v-model="queryParams.infoSecurityInvest" placeholder="请输入信息安全投入" clearable @keyup.enter="handleQuery" />
              </el-form-item>
              <el-form-item label="项目内容描述" prop="projectContent">
                <el-input v-model="queryParams.projectContent" placeholder="请输入项目内容描述" clearable @keyup.enter="handleQuery" />
              </el-form-item>
            </div>

            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>

              <el-button type="text" @click="toggleMore">
                {{ showMore ? '收起搜索条件' : '更多搜索条件' }}
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button v-hasPermi="['itsm:projectInfo:add']" type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['itsm:projectInfo:edit']" type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['itsm:projectInfo:remove']" type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['itsm:projectInfo:export']" type="warning" plain icon="Download" @click="handleExport">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @query-table="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="projectInfoList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <!--        <el-table-column label="项目id" align="center" prop="projectId" v-if="true" />-->
        <el-table-column label="序号" align="center" fixed="left" width="55">
          <template #default="scope">
            <span>{{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column label="项目名称" align="center" prop="projectName" fixed="left" width="300" />
        <el-table-column label="项目状态" align="center" prop="projectStatus">
          <template #default="scope">
            <dict-tag :options="project_status" :value="scope.row.projectStatus" />
          </template>
        </el-table-column>
        <el-table-column label="建设责任部门" align="center" prop="constructRespDept" />
        <el-table-column label="部门负责人" align="center" prop="deptManager" />
        <el-table-column label="负责人电话" align="center" prop="deptManagerPhone" />
        <el-table-column label="工作联系人" align="center" prop="workLinkman" />
        <el-table-column label="联系人电话" align="center" prop="workLinkmanPhone" />
        <el-table-column label="计划验收时间" align="center" prop="planAcceptanceDate" width="120">
          <template #default="scope">
            <span>{{ parseTime(scope.row.planAcceptanceDate, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="计划上线时间" align="center" prop="planOnlineDate" width="120">
          <template #default="scope">
            <span>{{ parseTime(scope.row.planOnlineDate, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="项目总金额(元)" align="center" prop="projectTotalAmount" />
        <el-table-column label="信息安全投入(元)" align="center" prop="infoSecurityInvest" />
        <!--        <el-table-column label="项目内容描述" align="center" prop="projectContent" />-->
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right" width="150">
          <template #default="scope">
            <el-tooltip content="详细" placement="top">
              <el-button v-hasPermi="['itsm:projectInfo:query']" link type="primary" icon="View" @click="openDetailDialog(scope.row)"> </el-button>
            </el-tooltip>
            <el-tooltip content="修改" placement="top">
              <el-button v-hasPermi="['itsm:projectInfo:edit']" link type="primary" icon="Edit" @click="handleUpdate(scope.row)"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button v-hasPermi="['itsm:projectInfo:remove']" link type="danger" icon="Delete" @click="handleDelete(scope.row)"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" :total="total" @pagination="getList" />
    </el-card>

    <!-- 添加或修改项目信息对话框 -->
    <el-dialog v-model="dialog.visible" :title="dialog.title" width="800px" append-to-body>
      <el-form ref="projectInfoFormRef" :model="form" :rules="rules" label-width="150px">
        <el-form-item label="项目名称" prop="projectName">
          <el-input v-model="form.projectName" placeholder="请输入项目名称" />
        </el-form-item>
        <el-form-item label="项目状态" prop="projectStatus">
          <!--          <el-radio-group v-model="form.projectStatus">-->
          <!--            <el-radio-->
          <!--              v-for="dict in project_status"-->
          <!--              :key="dict.value"-->
          <!--              :value="dict.value"-->
          <!--            >{{dict.label}}</el-radio>-->
          <!--          </el-radio-group>-->
          <el-select v-model="form.projectStatus" placeholder="请选择项目状态" clearable>
            <el-option v-for="dict in project_status" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="建设责任部门" prop="constructRespDept">
          <el-input v-model="form.constructRespDept" placeholder="请输入建设责任部门" />
        </el-form-item>
        <el-form-item label="部门负责人" prop="deptManager">
          <el-input v-model="form.deptManager" placeholder="请输入部门负责人" />
        </el-form-item>
        <el-form-item label="负责人电话" prop="deptManagerPhone">
          <el-input v-model="form.deptManagerPhone" placeholder="请输入负责人电话" />
        </el-form-item>
        <el-form-item label="工作联系人" prop="workLinkman">
          <el-input v-model="form.workLinkman" placeholder="请输入工作联系人" />
        </el-form-item>
        <el-form-item label="联系人电话" prop="workLinkmanPhone">
          <el-input v-model="form.workLinkmanPhone" placeholder="请输入联系人电话" />
        </el-form-item>
        <el-form-item label="计划验收时间" prop="planAcceptanceDate">
          <el-date-picker v-model="form.planAcceptanceDate" clearable type="date" value-format="YYYY-MM-DD" placeholder="请选择计划验收时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="计划上线时间" prop="planOnlineDate">
          <el-date-picker v-model="form.planOnlineDate" clearable type="date" value-format="YYYY-MM-DD" placeholder="请选择计划上线时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="项目总金额(元)" prop="projectTotalAmount">
          <el-input v-model="form.projectTotalAmount" placeholder="请输入项目总金额(元)" />
        </el-form-item>
        <el-form-item label="信息安全投入(元)" prop="infoSecurityInvest">
          <el-input v-model="form.infoSecurityInvest" placeholder="请输入信息安全投入(元)" />
        </el-form-item>
        <el-form-item label="项目内容描述" prop="projectContent">
          <el-input v-model="form.projectContent" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div v-if="dialog.showBtn" class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!--  详细按钮弹窗  -->
    <el-dialog v-model="isDetailVisible" title="项目详情" width="80%" @closed="onClosed">
      <el-tabs v-model="activeTab" @tab-click="handleTabClick">
        <el-tab-pane label="项目信息管理" name="projectInfo">
          <ProjectInfoTab :project="selectedProject" />
        </el-tab-pane>
        <el-tab-pane label="系统信息管理" name="sysInfo">
          <SysInfoTab :project="selectedProject" />
        </el-tab-pane>
        <el-tab-pane label="服务器信息管理" name="serverInfo">
          <ServerInfoTab :project="selectedProject" />
        </el-tab-pane>
        <el-tab-pane label="项目人员管理" name="projectPersonnel">
          <ProjectUserTab :project="selectedProject" />
        </el-tab-pane>
        <el-tab-pane label="安全信息管理" name="secureInfo">
          <SecureInfoTab :project="selectedProject" />
        </el-tab-pane>
        <el-tab-pane label="资金管理" name="fundInfo">
          <fundInfoTab :project="selectedProject" />
        </el-tab-pane>
      </el-tabs>
    </el-dialog>

    <!-- 项目明细 -->
    <!--    <ProjectDetail :project="selectedProject" v-model:visible="dialogVisible" />-->

    <!-- 项目明细 -->
    <!--    <ProjectDetail ref="projectDetailRef" :projectId="selectedProjectId"></ProjectDetail>-->
  </div>
</template>

<script setup name="ProjectInfo" lang="ts">
import { listProjectInfo, getProjectInfo, delProjectInfo, addProjectInfo, updateProjectInfo } from '@/api/itsm/projectInfo';
import { ProjectInfoVO, ProjectInfoQuery, ProjectInfoForm } from '@/api/itsm/projectInfo/types';
import ProjectInfoTab from '@/views/itsm/projectInfo/components/ProjectInfoTab.vue';
import SysInfoTab from '@/views/itsm/projectInfo/components/SysInfoTab.vue';
import ServerInfoTab from '@/views/itsm/projectInfo/components/ServerInfoTab.vue';
import ProjectUserTab from '@/views/itsm/projectInfo/components/ProjectUserTab.vue';
import FundInfoTab from '@/views/itsm/projectInfo/components/fundInfoTab.vue';
import SecureInfoTab from '@/views/itsm/projectInfo/components/secureInfoTab.vue';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { project_status } = toRefs<any>(proxy?.useDict('project_status'));

const projectInfoList = ref<ProjectInfoVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const projectInfoFormRef = ref<ElFormInstance>();

const activeTab = ref('projectInfo');
const isDetailVisible = ref(false);

const selectedProject = ref<ProjectInfoVO | null>(null);
const projectInfoRef = ref<InstanceType<typeof ProjectInfoTab> | null>(null);
const sysInfoRef = ref<InstanceType<typeof SysInfoTab> | null>(null);
const serverInfoRef = ref<InstanceType<typeof ServerInfoTab> | null>(null);
const projectPersonnelRef = ref<InstanceType<typeof ProjectUserTab> | null>(null);

const secureInfoRef = ref<InstanceType<typeof SecureInfoTab> | null>(null);
const fundInfoRef = ref<InstanceType<typeof FundInfoTab> | null>(null);

const onClosed = () => {
  isDetailVisible.value = false;
  activeTab.value = 'projectInfo';
};

// 所选择的项目 id
const selectedProjectId = ref('');

const dialog = reactive<DialogOption>({
  visible: false,
  title: '',
  showBtn: true
});

const initFormData: ProjectInfoForm = {
  projectId: undefined,
  projectName: undefined,
  projectStatus: undefined,
  constructRespDept: undefined,
  deptManager: undefined,
  deptManagerPhone: undefined,
  workLinkman: undefined,
  workLinkmanPhone: undefined,
  planAcceptanceDate: undefined,
  planOnlineDate: undefined,
  projectTotalAmount: undefined,
  infoSecurityInvest: undefined,
  projectContent: undefined
};

const data = reactive<PageData<ProjectInfoForm, ProjectInfoQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    projectName: undefined,
    projectStatus: undefined,
    constructRespDept: undefined,
    deptManager: undefined,
    deptManagerPhone: undefined,
    workLinkman: undefined,
    workLinkmanPhone: undefined,
    planAcceptanceDate: undefined,
    planOnlineDate: undefined,
    projectTotalAmount: undefined,
    infoSecurityInvest: undefined,
    projectContent: undefined,
    params: {}
  },
  rules: {
    projectId: [{ required: true, message: '项目id不能为空', trigger: 'blur' }],
    projectName: [{ required: true, message: '项目名称不能为空', trigger: 'blur' }],
    projectStatus: [{ required: true, message: '项目状态不能为空', trigger: 'change' }],
    constructRespDept: [{ required: true, message: '建设责任部门不能为空', trigger: 'blur' }],
    deptManager: [{ required: true, message: '部门负责人不能为空', trigger: 'blur' }],
    deptManagerPhone: [{ required: true, message: '负责人电话不能为空', trigger: 'blur' }],
    workLinkman: [{ required: true, message: '工作联系人不能为空', trigger: 'blur' }],
    workLinkmanPhone: [{ required: true, message: '联系人电话不能为空', trigger: 'blur' }],
    planAcceptanceDate: [{ required: true, message: '计划验收时间不能为空', trigger: 'blur' }],
    planOnlineDate: [{ required: true, message: '计划上线时间不能为空', trigger: 'blur' }],
    projectTotalAmount: [{ required: true, message: '项目总金额不能为空', trigger: 'blur' }],
    infoSecurityInvest: [{ required: true, message: '信息安全投入不能为空', trigger: 'blur' }],
    projectContent: [{ required: true, message: '项目内容描述不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询项目信息列表 */
const getList = async () => {
  loading.value = true;
  const res = await listProjectInfo(queryParams.value);
  projectInfoList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  projectInfoFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: ProjectInfoVO[]) => {
  ids.value = selection.map((item) => item.projectId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加项目信息';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: ProjectInfoVO) => {
  loading.value = true;
  reset();
  const _projectId = row?.projectId || ids.value[0];
  const res = await getProjectInfo(_projectId);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改项目信息';
  loading.value = false;
};

/** 查看按钮操作 */
const handleView = async (row?: ProjectInfoVO) => {
  loading.value = true;
  reset();
  const _projectId = row?.projectId || ids.value[0];
  const res = await getProjectInfo(_projectId);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.showBtn = false;
  dialog.title = '项目信息详细';
  selectedProjectId.value = _projectId;
  loading.value = false;
};

const handleTabClick = (tab: any) => {
  // 根据选中的标签页执行相应的API请求
  switch (tab.props.name) {
    case 'projectInfo':
      projectInfoRef.value?.fetchData();
      break;
    case 'sysInfo':
      sysInfoRef.value?.fetchData();
      break;
    case 'serverInfo':
      serverInfoRef.value?.fetchData();
      break;
    case 'projectPersonnel':
      projectPersonnelRef.value?.fetchData();
      break;
    case 'secureInfo':
      secureInfoRef.value?.fetchData();
      break;
    case 'fundInfo':
      fundInfoRef.value?.fetchData();
      break;
    default:
      console.log('没有更多页签');
      break;
  }
};

/** 查看按钮操作 */
const openDetailDialog = (row?: ProjectInfoVO) => {
  console.log('用户点击了详情row：' + JSON.stringify(row, null, 4));
  selectedProject.value = row;
  isDetailVisible.value = true;
  console.log('isDetailVisible:', isDetailVisible.value); // 确认 isDetailVisible 被设置为 true
};

/** 提交按钮 */
const submitForm = () => {
  projectInfoFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.projectId) {
        await updateProjectInfo(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addProjectInfo(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: ProjectInfoVO) => {
  const _projectIds = row?.projectId || ids.value;
  await proxy?.$modal.confirm('是否确认删除项目信息编号为"' + _projectIds + '"的数据项？').finally(() => (loading.value = false));
  await delProjectInfo(_projectIds);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  const formattedDate = formatCurrentDate('YYYY-MM-DD HH-mm-ss');
  proxy?.download(
    'itsm/projectInfo/export',
    {
      ...queryParams.value
    },
    `项目信息_${formattedDate}.xlsx`
  );
};

// 格式化日期的函数
const formatCurrentDate = (format) => {
  const date = new Date();
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');

  if (format === 'YYYY-MM-DD HH:mm:ss') {
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  } else {
    throw new Error('Unsupported date format');
  }
};

// 显示控制
const showMore = ref(false);
const toggleMore = () => {
  showMore.value = !showMore.value;
};

onMounted(() => {
  getList();
});
</script>
