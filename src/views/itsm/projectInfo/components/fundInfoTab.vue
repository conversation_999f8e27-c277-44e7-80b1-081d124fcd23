<template>
  <el-table v-loading="loading" :data="fundInfoList">
    <el-table-column label="序号" align="center" fixed="left">
      <template #default="scope">
        <span>{{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}</span>
      </template>
    </el-table-column>
    <!--        <el-table-column label="资金id" align="center" prop="fundId" v-if="true" />-->
    <el-table-column label="所属项目" align="center" prop="projectId">
      <template #default="scope">
        <div v-for="project in projectInfoList">
          <span v-if="project.projectId === scope.row.projectId">{{ project.projectName }}</span>
        </div>
      </template>
    </el-table-column>
    <el-table-column label="款项批次" align="center" prop="paymentBatch" />
    <el-table-column label="计划付款金额" align="center" prop="planAmount" />
    <el-table-column label="计划付款日期" align="center" prop="planPaymentTime" width="180">
      <template #default="scope">
        <span>{{ parseTime(scope.row.planPaymentTime, '{y}-{m}-{d}') }}</span>
      </template>
    </el-table-column>
    <el-table-column label="已付款金额" align="center" prop="paidAmount" />
    <el-table-column label="实际付款日期" align="center" prop="actualPaymentTime" width="180">
      <template #default="scope">
        <span>{{ parseTime(scope.row.actualPaymentTime, '{y}-{m}-{d}') }}</span>
      </template>
    </el-table-column>
  </el-table>

  <pagination v-show="total > 0" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" :total="total" @pagination="getList" />
</template>

<script setup lang="ts">
import { FundInfoForm, FundInfoQuery, FundInfoVO } from '@/api/itsm/fundInfo/types';
import { ProjectInfoVO } from '@/api/itsm/projectInfo/types';
import { listProjectInfo } from '@/api/itsm/projectInfo';
import { PropType } from 'vue';
import { listFundInfo } from '@/api/itsm/fundInfo';

const loading = ref(true);
const fundInfoList = ref<FundInfoVO[]>([]);
const total = ref(0);
const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const initFormData: FundInfoForm = {
  fundId: undefined,
  projectId: undefined,
  paymentBatch: undefined,
  planAmount: undefined,
  planPaymentTime: undefined,
  paidAmount: undefined,
  actualPaymentTime: undefined
};
const data = reactive<PageData<FundInfoForm, FundInfoQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    projectId: undefined,
    paymentBatch: undefined,
    planAmount: undefined,
    planPaymentTime: undefined,
    paidAmount: undefined,
    actualPaymentTime: undefined,
    params: {}
  },
  rules: {
    fundId: [{ required: true, message: '资金id不能为空', trigger: 'blur' }],
    projectId: [{ required: true, message: '所属项目id不能为空', trigger: 'blur' }],
    paymentBatch: [{ required: true, message: '款项批次不能为空', trigger: 'blur' }],
    planAmount: [{ required: true, message: '计划付款金额不能为空', trigger: 'blur' }],
    planPaymentTime: [{ required: true, message: '计划付款日期不能为空', trigger: 'blur' }],
    paidAmount: [{ required: true, message: '已付款金额不能为空', trigger: 'blur' }],
    actualPaymentTime: [{ required: true, message: '实际付款日期不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

const props = defineProps({
  project: {
    type: Object as PropType<ProjectInfoVO>,
    required: true
  }
});

// 项目信息
const projectInfoList = ref<ProjectInfoVO[]>([]);

const getProjectInfoList = async () => {
  const res = await listProjectInfo({});
  projectInfoList.value = res.rows;
};

/** 查询资金信息列表 */
const getList = async () => {
  loading.value = true;
  queryParams.value.projectId = props.project.projectId;
  const res = await listFundInfo(queryParams.value);
  fundInfoList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

const fetchData = () => {
  getList();
};

watch(
  () => props.project.projectId,
  (newId) => {
    if (newId) {
      fetchData();
    }
  },
  { immediate: true }
);

onMounted(() => {
  getList();
  getProjectInfoList();
});
</script>

<style scoped lang="scss"></style>
