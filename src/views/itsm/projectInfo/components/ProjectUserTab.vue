<template>
  <el-table v-loading="loading" :data="projectUserList">
    <el-table-column label="序号" align="center" fixed="left" width="55">
      <template #default="scope">
        <span>{{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}</span>
      </template>
    </el-table-column>
    <el-table-column label="用户姓名" align="center" prop="sysUserId" fixed="left">
      <template #default="scope">
        <div v-for="user in userList">
          <span v-if="user.userId === scope.row.sysUserId">{{ user.nickName }}</span>
        </div>
      </template>
    </el-table-column>
    <el-table-column label="所属项目" align="center" prop="projectId" width="200">
      <template #default="scope">
        <div v-for="project in projectInfoList">
          <span v-if="project.projectId === scope.row.projectId">{{ project.projectName }}</span>
        </div>
      </template>
    </el-table-column>
    <el-table-column label="所属系统" align="center" prop="systemId" width="200">
      <template #default="scope">
        <div v-for="system in systemInfoList">
          <span v-if="system.systemId === scope.row.systemId">{{ system.systemName }}</span>
        </div>
      </template>
    </el-table-column>
    <el-table-column label="身份证号" align="center" prop="idCard" />
    <el-table-column label="是否在岗" align="center" prop="atWorkFlag">
      <template #default="scope">
        <dict-tag :options="sys_yes_no" :value="scope.row.atWorkFlag" />
      </template>
    </el-table-column>
    <el-table-column label="服务开始时间" align="center" prop="serviceBeginDate" width="180">
      <template #default="scope">
        <span>{{ parseTime(scope.row.serviceBeginDate, '{y}-{m}-{d}') }}</span>
      </template>
    </el-table-column>
    <el-table-column label="用户所属公司名" align="center" prop="userCompanyName" />
    <el-table-column label="职位" align="center" prop="position" />
    <el-table-column label="政治面貌" align="center" prop="politicsStatus">
      <template #default="scope">
        <dict-tag :options="politics_status" :value="scope.row.politicsStatus" />
      </template>
    </el-table-column>
    <el-table-column label="学历" align="center" prop="eduBackground">
      <template #default="scope">
        <dict-tag :options="edu_background" :value="scope.row.eduBackground" />
      </template>
    </el-table-column>
    <el-table-column label="学位" align="center" prop="acaDegree" />
    <el-table-column label="专业" align="center" prop="major" />
    <el-table-column label="毕业院校" align="center" prop="gradSchool" />
    <el-table-column label="联系电话" align="center" prop="contactPhone" />
    <el-table-column label="是否有配偶\直系血亲\三代以内旁系血亲\近姻亲在我单位工作情况" align="center" prop="haveRelativesInUnit">
      <template #default="scope">
        <dict-tag :options="sys_yes_no" :value="scope.row.haveRelativesInUnit" />
      </template>
    </el-table-column>
    <el-table-column label="紧急联系人及联系电话" align="center" prop="emergencyInfo" />
    <el-table-column label="是否开通vpn" align="center" prop="vpnFlag">
      <template #default="scope">
        <dict-tag :options="sys_yes_no" :value="scope.row.vpnFlag" />
      </template>
    </el-table-column>
  </el-table>

  <pagination v-show="total > 0" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" :total="total" @pagination="getList" />
</template>

<script setup lang="ts">
import { ProjectUserForm, ProjectUserQuery, ProjectUserVO } from '@/api/itsm/projectUser/types';
import { listProjectUser } from '@/api/itsm/projectUser';
import { ProjectInfoVO } from '@/api/itsm/projectInfo/types';
import { listProjectInfo } from '@/api/itsm/projectInfo';
import { SystemInfoVO } from '@/api/itsm/systemInfo/types';
import { listSystemInfo } from '@/api/itsm/systemInfo';
import { UserVO } from '@/api/system/user/types';
import { listUser } from '@/api/system/user';
import { PropType } from 'vue';

const props = defineProps({
  project: {
    type: Object as PropType<ProjectInfoVO>,
    required: true
  }
});

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { edu_background, sys_yes_no, politics_status } = toRefs<any>(proxy?.useDict('edu_background', 'sys_yes_no', 'politics_status'));

const projectUserList = ref<ProjectUserVO[]>([]);
const total = ref(0);

const initFormData: ProjectUserForm = {
  userId: undefined,
  sysUserId: undefined,
  projectId: undefined,
  systemId: undefined,
  idCard: undefined,
  atWorkFlag: undefined,
  serviceBeginDate: undefined,
  userCompanyName: undefined,
  position: undefined,
  politicsStatus: undefined,
  eduBackground: undefined,
  acaDegree: undefined,
  major: undefined,
  gradSchool: undefined,
  contactPhone: undefined,
  haveRelativesInUnit: undefined,
  emergencyInfo: undefined,
  vpnFlag: undefined
};
const data = reactive<PageData<ProjectUserForm, ProjectUserQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    sysUserId: undefined,
    projectId: undefined,
    systemId: undefined,
    idCard: undefined,
    atWorkFlag: undefined,
    serviceBeginDate: undefined,
    userCompanyName: undefined,
    position: undefined,
    politicsStatus: undefined,
    eduBackground: undefined,
    acaDegree: undefined,
    major: undefined,
    gradSchool: undefined,
    contactPhone: undefined,
    haveRelativesInUnit: undefined,
    emergencyInfo: undefined,
    vpnFlag: undefined,
    params: {}
  },
  rules: {
    userId: [{ required: true, message: '人员id不能为空', trigger: 'blur' }],
    sysUserId: [{ required: true, message: '所属系统用户id不能为空', trigger: 'blur' }],
    projectId: [{ required: true, message: '所属项目id不能为空', trigger: 'blur' }],
    systemId: [{ required: false, message: '所属系统id不能为空', trigger: 'blur' }],
    idCard: [{ required: true, message: '身份证号不能为空', trigger: 'blur' }],
    atWorkFlag: [{ required: true, message: '是否在岗不能为空', trigger: 'change' }],
    serviceBeginDate: [{ required: true, message: '服务开始时间不能为空', trigger: 'blur' }],
    userCompanyName: [{ required: true, message: '用户所属公司名不能为空', trigger: 'blur' }],
    position: [{ required: true, message: '职位不能为空', trigger: 'blur' }],
    politicsStatus: [{ required: true, message: '政治面貌不能为空', trigger: 'change' }],
    eduBackground: [{ required: true, message: '学历不能为空', trigger: 'change' }],
    acaDegree: [{ required: true, message: '学位不能为空', trigger: 'blur' }],
    major: [{ required: true, message: '专业不能为空', trigger: 'blur' }],
    gradSchool: [{ required: true, message: '毕业院校不能为空', trigger: 'blur' }],
    contactPhone: [{ required: true, message: '联系电话不能为空', trigger: 'blur' }],
    haveRelativesInUnit: [{ required: true, message: '是否有配偶\直系血亲\三代以内旁系血亲\近姻亲在我单位工作情况不能为空', trigger: 'change' }],
    emergencyInfo: [{ required: true, message: '紧急联系人及联系电话不能为空', trigger: 'blur' }],
    vpnFlag: [{ required: true, message: '是否开通vpn不能为空', trigger: 'change' }]
  }
});

const { queryParams, form, rules } = toRefs(data);
const loading = ref(true);

/** 查询项目人员信息列表 */
const getList = async () => {
  loading.value = true;
  queryParams.value.projectId = props.project.projectId;
  console.log('项目模块查看，人员模块查询参数：' + JSON.stringify(queryParams.value, null, 4));
  const res = await listProjectUser(queryParams.value);
  console.log('项目模块查看，人员模块查询结果：' + JSON.stringify(res, null, 4));
  projectUserList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

const fetchData = () => {
  getList();
};

watch(
  () => props.project.projectId,
  (newId) => {
    if (newId) {
      fetchData();
    }
  },
  { immediate: true }
);

/**
 * 项目信息列表
 */
const projectInfoList = ref<ProjectInfoVO[]>([]);
const getProjectInfoList = async () => {
  const res = await listProjectInfo({});
  projectInfoList.value = res.rows;
};
/**
 * 系统信息列表
 */
const systemInfoList = ref<SystemInfoVO[]>([]);
const getSystemInfoList = async () => {
  const res = await listSystemInfo(queryParams.value);
  systemInfoList.value = res.rows;
};

/**
 * 用户信息
 */
const userList = ref<UserVO[]>();
const getUserList = async () => {
  const res = await listUser();
  userList.value = res.rows;
};

onMounted(() => {
  getList();
  getProjectInfoList();
  getSystemInfoList();
  getUserList();
});
</script>
