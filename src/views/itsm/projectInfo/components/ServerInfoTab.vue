<template>
  <el-table v-loading="loading" :data="serverInfoList">
    <el-table-column label="序号" align="center" fixed="left" width="55">
      <template #default="scope">
        <span>{{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}</span>
      </template>
    </el-table-column>
    <el-table-column label="所属系统" align="center" prop="systemId" fixed="left" width="200">
      <template #default="scope">
        <div v-for="system in systemInfoList">
          <span v-if="system.systemId === scope.row.systemId">{{ system.systemName }}</span>
        </div>
      </template>
    </el-table-column>
    <el-table-column label="服务器名称" align="center" prop="serverName" fixed="left" width="200" />
    <el-table-column label="服务器类型" align="center" prop="serverType" width="120">
      <template #default="scope">
        <dict-tag :options="server_type" :value="scope.row.serverType" />
      </template>
    </el-table-column>
    <el-table-column label="其他类型" align="center" prop="otherType" />
    <el-table-column label="是否部署中山市政务云平台" align="center" prop="zsGovCloudFlag">
      <template #default="scope">
        <dict-tag :options="sys_yes_no" :value="scope.row.zsGovCloudFlag" />
      </template>
    </el-table-column>
    <el-table-column label="云主机托管位置" align="center" prop="cloudLocation" />
    <el-table-column label="操作系统名称及版本" align="center" prop="osNameVersion" />
    <el-table-column label="开发工具" align="center" prop="devTool" />
    <el-table-column label="数据库名称及版本" align="center" prop="dbNameVersion" />
    <el-table-column label="数据库容量" align="center" prop="dbCapacity" />
    <el-table-column label="中间件名称及版本" align="center" prop="middlewareNameVersion" />
    <el-table-column label="其他软件" align="center" prop="otherSoftware" />
    <el-table-column label="党政网络ip" align="center" prop="govNetworkIp" width="120" />
    <el-table-column label="互联网ip" align="center" prop="internetIp" width="120" />
    <el-table-column label="云主机ipv4地址" align="center" prop="cloudServerIpv4" width="120" />
    <el-table-column label="云主机ipv6地址" align="center" prop="cloudServerIpv6" width="120" />
    <el-table-column label="互联网访问地址" align="center" prop="internetUrl" />
    <el-table-column label="政务网络访问地址" align="center" prop="govNetworkUrl" />
    <el-table-column label="系统后台地址" align="center" prop="systemAdminUrl" />
    <el-table-column label="cpu" align="center" prop="cpu" />
    <el-table-column label="内存" align="center" prop="memory" />
    <el-table-column label="系统盘" align="center" prop="systemHardDisk" />
    <el-table-column label="数据盘" align="center" prop="dataHardDisk" />
    <el-table-column label="申请工单号" align="center" prop="applyOrderNo" />
    <el-table-column label="开通端口信息" align="center" prop="openPortInfo" />
    <el-table-column label="端口用途" align="center" prop="openPortUse" />
  </el-table>

  <pagination v-show="total > 0" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" :total="total" @pagination="getList" />
</template>

<script setup lang="ts">
import { ServerInfoForm, ServerInfoQuery, ServerInfoVO } from '@/api/itsm/serverInfo/types';
import { SystemInfoVO } from '@/api/itsm/systemInfo/types';
import { listSystemInfo } from '@/api/itsm/systemInfo';
import { listServerInfo } from '@/api/itsm/serverInfo';
import { PropType } from 'vue';
import { ProjectInfoVO } from '@/api/itsm/projectInfo/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { sys_yes_no, server_type } = toRefs<any>(proxy?.useDict('sys_yes_no', 'server_type'));

const loading = ref(true);
const serverInfoList = ref<ServerInfoVO[]>([]);
const total = ref(0);

const initFormData: ServerInfoForm = {
  serverId: undefined,
  systemId: undefined,
  serverName: undefined,
  serverType: undefined,
  otherType: undefined,
  zsGovCloudFlag: undefined,
  cloudLocation: undefined,
  osNameVersion: undefined,
  devTool: undefined,
  dbNameVersion: undefined,
  dbCapacity: undefined,
  middlewareNameVersion: undefined,
  otherSoftware: undefined,
  govNetworkIp: undefined,
  internetIp: undefined,
  cloudServerIpv4: undefined,
  cloudServerIpv6: undefined,
  internetUrl: undefined,
  govNetworkUrl: undefined,
  systemAdminUrl: undefined,
  cpu: undefined,
  memory: undefined,
  systemHardDisk: undefined,
  dataHardDisk: undefined,
  applyOrderNo: undefined,
  openPortInfo: undefined,
  openPortUse: undefined
};
const data = reactive<PageData<ServerInfoForm, ServerInfoQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    systemId: undefined,
    serverName: undefined,
    serverType: undefined,
    otherType: undefined,
    zsGovCloudFlag: undefined,
    cloudLocation: undefined,
    osNameVersion: undefined,
    devTool: undefined,
    dbNameVersion: undefined,
    dbCapacity: undefined,
    middlewareNameVersion: undefined,
    otherSoftware: undefined,
    govNetworkIp: undefined,
    internetIp: undefined,
    cloudServerIpv4: undefined,
    cloudServerIpv6: undefined,
    internetUrl: undefined,
    govNetworkUrl: undefined,
    systemAdminUrl: undefined,
    cpu: undefined,
    memory: undefined,
    systemHardDisk: undefined,
    dataHardDisk: undefined,
    applyOrderNo: undefined,
    openPortInfo: undefined,
    openPortUse: undefined,
    params: {}
  },
  rules: {
    serverId: [{ required: true, message: '服务器id不能为空', trigger: 'blur' }],
    systemId: [{ required: true, message: '所属系统id不能为空', trigger: 'blur' }],
    serverName: [{ required: true, message: '服务器名称不能为空', trigger: 'blur' }],
    serverType: [{ required: true, message: '服务器类型不能为空', trigger: 'change' }],
    otherType: [{ required: false, message: '其他类型不能为空', trigger: 'blur' }],
    zsGovCloudFlag: [{ required: true, message: '是否部署中山市政务云平台不能为空', trigger: 'change' }],
    cloudLocation: [{ required: false, message: '云主机托管位置不能为空', trigger: 'blur' }],
    osNameVersion: [{ required: true, message: '操作系统名称及版本不能为空', trigger: 'blur' }],
    devTool: [{ required: false, message: '开发工具不能为空', trigger: 'blur' }],
    dbNameVersion: [{ required: false, message: '数据库名称及版本不能为空', trigger: 'blur' }],
    dbCapacity: [{ required: false, message: '数据库容量不能为空', trigger: 'blur' }],
    middlewareNameVersion: [{ required: false, message: '中间件名称及版本不能为空', trigger: 'blur' }],
    otherSoftware: [{ required: false, message: '其他软件不能为空', trigger: 'blur' }],
    govNetworkIp: [{ required: false, message: '党政网络ip不能为空', trigger: 'blur' }],
    internetIp: [{ required: false, message: '互联网ip不能为空', trigger: 'blur' }],
    cloudServerIpv4: [{ required: false, message: '云主机ipv4地址不能为空', trigger: 'blur' }],
    cloudServerIpv6: [{ required: false, message: '云主机ipv6地址不能为空', trigger: 'blur' }],
    internetUrl: [{ required: false, message: '互联网访问地址不能为空', trigger: 'blur' }],
    govNetworkUrl: [{ required: false, message: '政务网络访问地址不能为空', trigger: 'blur' }],
    systemAdminUrl: [{ required: false, message: '系统后台地址不能为空', trigger: 'blur' }],
    cpu: [{ required: false, message: 'cpu不能为空', trigger: 'blur' }],
    memory: [{ required: false, message: '内存不能为空', trigger: 'blur' }],
    systemHardDisk: [{ required: false, message: '系统盘不能为空', trigger: 'blur' }],
    dataHardDisk: [{ required: false, message: '数据盘不能为空', trigger: 'blur' }],
    applyOrderNo: [{ required: false, message: '申请工单号不能为空', trigger: 'blur' }],
    openPortInfo: [{ required: false, message: '开通端口信息不能为空', trigger: 'blur' }],
    openPortUse: [{ required: false, message: '端口用途不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

const props = defineProps({
  project: {
    type: Object as PropType<ProjectInfoVO>,
    required: true
  }
});

/** 查询服务器信息列表 */
const getList = async () => {
  loading.value = true;
  //先根据项目查系统
  const systemInfoList = await listSystemInfo({ projectId: props.project.projectId });
  queryParams.value.systemIds = systemInfoList.rows.map((item) => item.systemId);

  const res = await listServerInfo(queryParams.value);
  serverInfoList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/**
 * 系统信息列表
 */
const systemInfoList = ref<SystemInfoVO[]>([]);
const getSystemInfoList = async () => {
  const res = await listSystemInfo(queryParams.value);
  systemInfoList.value = res.rows;
};

const fetchData = () => {
  getList();
};

watch(
  () => props.project.projectId,
  (newId) => {
    if (newId) {
      fetchData();
    }
  },
  { immediate: true }
);

onMounted(() => {
  getList();
  getSystemInfoList();
});
</script>
