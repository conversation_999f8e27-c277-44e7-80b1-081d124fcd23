<template>
  <el-table v-loading="loading" :data="secureInfoList">
    <el-table-column type="selection" width="55" align="center" />
    <el-table-column label="序号" align="center" fixed="left">
      <template #default="scope">
        <span>{{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}</span>
      </template>
    </el-table-column>
    <!--        <el-table-column label="所属项目" align="center" prop="projectId">-->
    <!--          <template #default="scope">-->
    <!--            <div v-for="project in projectInfoList">-->
    <!--              <span v-if="project.projectId === scope.row.projectId">{{ project.projectName }}</span>-->
    <!--            </div>-->
    <!--          </template>-->
    <!--        </el-table-column>-->
    <el-table-column label="所属系统" align="center" prop="systemId" width="200">
      <template #default="scope">
        <div v-for="system in systemInfoList">
          <span v-if="system.systemId === scope.row.systemId">{{ system.systemName }}</span>
        </div>
      </template>
    </el-table-column>
    <el-table-column label="信息安全投入" align="center" prop="investFund" />
    <el-table-column label="信息安全等保级别" align="center" prop="secureLevel" />
    <el-table-column label="是否完成等保测评" align="center" prop="atInsuranceFlag">
      <template #default="scope">
        <dict-tag :options="sys_yes_no" :value="scope.row.atInsuranceFlag" />
      </template>
    </el-table-column>
    <el-table-column label="是否密码测评" align="center" prop="pwdFlag">
      <template #default="scope">
        <dict-tag :options="sys_yes_no" :value="scope.row.pwdFlag" />
      </template>
    </el-table-column>
    <el-table-column label="互联网地址" align="center" prop="internet" />
    <el-table-column label="是否对接物联网" align="center" prop="iotFlag">
      <template #default="scope">
        <dict-tag :options="sys_yes_no" :value="scope.row.atInsuranceFlag" />
      </template>
    </el-table-column>
    <el-table-column label="是否对接视频共享平台" align="center" prop="videoFlag">
      <template #default="scope">
        <dict-tag :options="sys_yes_no" :value="scope.row.videoFlag" />
      </template>
    </el-table-column>
    <el-table-column label="互联网接口地址" align="center" prop="internetInterface" />
    <el-table-column label="暴露互联网的端口" align="center" prop="port" />
  </el-table>

  <pagination v-show="total > 0" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" :total="total" @pagination="getList" />
</template>

<script setup lang="ts">
import { SecureInfoForm, SecureInfoQuery, SecureInfoVO } from '@/api/itsm/secureInfo/types';
import { SystemInfoVO } from '@/api/itsm/systemInfo/types';
import { listSystemInfo } from '@/api/itsm/systemInfo';
import { listSecureInfo } from '@/api/itsm/secureInfo';
import { PropType } from 'vue';
import { ProjectInfoVO } from '@/api/itsm/projectInfo/types';

const loading = ref(true);
const secureInfoList = ref<SecureInfoVO[]>([]);
const total = ref(0);

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const { edu_background, sys_yes_no, politics_status, itsm_secure_level } = toRefs<any>(
  proxy?.useDict('edu_background', 'sys_yes_no', 'politics_status', 'itsm_secure_level')
);

const props = defineProps({
  project: {
    type: Object as PropType<ProjectInfoVO>,
    required: true
  }
});

const initFormData: SecureInfoForm = {
  secureId: undefined,
  projectId: undefined,
  systemId: undefined,
  investFund: undefined,
  secureLevel: undefined,
  atInsuranceFlag: undefined,
  pwdFlag: undefined,
  internet: undefined,
  iotFlag: undefined,
  videoFlag: undefined,
  internetInterface: undefined,
  port: undefined
};
const data = reactive<PageData<SecureInfoForm, SecureInfoQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    projectId: undefined,
    systemId: undefined,
    investFund: undefined,
    secureLevel: undefined,
    atInsuranceFlag: undefined,
    pwdFlag: undefined,
    internet: undefined,
    iotFlag: undefined,
    videoFlag: undefined,
    internetInterface: undefined,
    port: undefined,
    params: {}
  },
  rules: {
    secureId: [{ required: true, message: '安全id不能为空', trigger: 'blur' }],
    projectId: [{ required: true, message: '所属项目id不能为空', trigger: 'blur' }],
    systemId: [{ required: false, message: '所属系统id不能为空', trigger: 'blur' }],
    investFund: [{ required: true, message: '信息安全投入不能为空', trigger: 'blur' }],
    secureLevel: [{ required: true, message: '信息安全等保级别不能为空', trigger: 'blur' }],
    atInsuranceFlag: [{ required: true, message: '是否完成等保测评不能为空', trigger: 'blur' }],
    pwdFlag: [{ required: true, message: '是否密码测评不能为空', trigger: 'blur' }],
    internet: [{ required: true, message: '互联网地址不能为空', trigger: 'blur' }],
    iotFlag: [{ required: true, message: '是否对接物联网不能为空', trigger: 'blur' }],
    videoFlag: [{ required: true, message: '是否对接视频共享平台不能为空', trigger: 'blur' }],
    internetInterface: [{ required: true, message: '互联网接口地址不能为空', trigger: 'blur' }],
    port: [{ required: true, message: '暴露互联网的端口不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

const seachLoading = ref(true);
const selectSystemInfoList = ref<SystemInfoVO[]>([]);
const handleProjectChange = async (value: any) => {
  seachLoading.value = true;
  const res = await listSystemInfo({ projectId: value });
  selectSystemInfoList.value = res.rows;
  seachLoading.value = false;
};

/**
 * 系统信息列表
 */
const systemInfoList = ref<SystemInfoVO[]>([]);
const getSystemInfoList = async () => {
  loading.value = true;
  const res = await listSystemInfo(queryParams.value);
  systemInfoList.value = res.rows;

  loading.value = false;
};

/** 查询安全信息列表 */
const getList = async () => {
  loading.value = true;
  queryParams.value.projectId = props.project.projectId;
  const res = await listSecureInfo(queryParams.value);
  console.log('安全信息组件根据项目id获取数据：' + JSON.stringify(res, null, 4));
  secureInfoList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

const fetchData = () => {
  getList();
};

watch(
  () => props.project.projectId,
  (newId) => {
    if (newId) {
      fetchData();
    }
  },
  { immediate: true }
);

onMounted(() => {
  getList();
  getSystemInfoList();
});
</script>

<style scoped lang="scss"></style>
