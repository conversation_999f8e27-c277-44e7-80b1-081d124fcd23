<template>
  <el-table v-loading="loading" :data="projectInfoList">
    <el-table-column label="序号" align="center" fixed="left" width="55">
      <template #default="scope">
        <span>{{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}</span>
      </template>
    </el-table-column>
    <el-table-column label="项目名称" align="center" prop="projectName" fixed="left" width="300" />
    <el-table-column label="项目状态" align="center" prop="projectStatus">
      <template #default="scope">
        <dict-tag :options="project_status" :value="scope.row.projectStatus" />
      </template>
    </el-table-column>
    <el-table-column label="建设责任部门" align="center" prop="constructRespDept" />
    <el-table-column label="部门负责人" align="center" prop="deptManager" />
    <el-table-column label="负责人电话" align="center" prop="deptManagerPhone" />
    <el-table-column label="工作联系人" align="center" prop="workLinkman" />
    <el-table-column label="联系人电话" align="center" prop="workLinkmanPhone" />
    <el-table-column label="计划验收时间" align="center" prop="planAcceptanceDate" width="120">
      <template #default="scope">
        <span>{{ parseTime(scope.row.planAcceptanceDate, '{y}-{m}-{d}') }}</span>
      </template>
    </el-table-column>
    <el-table-column label="计划上线时间" align="center" prop="planOnlineDate" width="120">
      <template #default="scope">
        <span>{{ parseTime(scope.row.planOnlineDate, '{y}-{m}-{d}') }}</span>
      </template>
    </el-table-column>
    <el-table-column label="项目总金额(元)" align="center" prop="projectTotalAmount" />
    <el-table-column label="信息安全投入(元)" align="center" prop="infoSecurityInvest" />
    <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right" width="150">
      <template #default="scope">
        <el-tooltip content="详细" placement="top">
          <el-button v-hasPermi="['itsm:projectInfo:query']" link type="primary" icon="View" @click="handleView(scope.row)"> </el-button>
        </el-tooltip>
      </template>
    </el-table-column>
  </el-table>

  <pagination v-show="total > 0" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" :total="total" @pagination="getList" />

  <!-- 添加或修改项目信息对话框 -->
  <el-dialog v-model="dialog.visible" :title="dialog.title" width="800px" append-to-body>
    <el-form ref="projectInfoFormRef" :model="form" label-width="150px" disabled>
      <el-form-item label="项目名称" prop="projectName">
        <el-input v-model="form.projectName" placeholder="请输入项目名称" />
      </el-form-item>
      <el-form-item label="项目状态" prop="projectStatus">
        <!--          <el-radio-group v-model="form.projectStatus">-->
        <!--            <el-radio-->
        <!--              v-for="dict in project_status"-->
        <!--              :key="dict.value"-->
        <!--              :value="dict.value"-->
        <!--            >{{dict.label}}</el-radio>-->
        <!--          </el-radio-group>-->
        <el-select v-model="form.projectStatus" placeholder="请选择项目状态" clearable>
          <el-option v-for="dict in project_status" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="建设责任部门" prop="constructRespDept">
        <el-input v-model="form.constructRespDept" placeholder="请输入建设责任部门" />
      </el-form-item>
      <el-form-item label="部门负责人" prop="deptManager">
        <el-input v-model="form.deptManager" placeholder="请输入部门负责人" />
      </el-form-item>
      <el-form-item label="负责人电话" prop="deptManagerPhone">
        <el-input v-model="form.deptManagerPhone" placeholder="请输入负责人电话" />
      </el-form-item>
      <el-form-item label="工作联系人" prop="workLinkman">
        <el-input v-model="form.workLinkman" placeholder="请输入工作联系人" />
      </el-form-item>
      <el-form-item label="联系人电话" prop="workLinkmanPhone">
        <el-input v-model="form.workLinkmanPhone" placeholder="请输入联系人电话" />
      </el-form-item>
      <el-form-item label="计划验收时间" prop="planAcceptanceDate">
        <el-date-picker v-model="form.planAcceptanceDate" clearable type="date" value-format="YYYY-MM-DD" placeholder="请选择计划验收时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="计划上线时间" prop="planOnlineDate">
        <el-date-picker v-model="form.planOnlineDate" clearable type="date" value-format="YYYY-MM-DD" placeholder="请选择计划上线时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="项目总金额(元)" prop="projectTotalAmount">
        <el-input v-model="form.projectTotalAmount" placeholder="请输入项目总金额(元)" />
      </el-form-item>
      <el-form-item label="信息安全投入(元)" prop="infoSecurityInvest">
        <el-input v-model="form.infoSecurityInvest" placeholder="请输入信息安全投入(元)" />
      </el-form-item>
      <el-form-item label="项目内容描述" prop="projectContent">
        <el-input v-model="form.projectContent" type="textarea" placeholder="请输入内容" />
      </el-form-item>
    </el-form>
  </el-dialog>
</template>

<script setup lang="ts">
import { PropType } from 'vue';
import { ProjectInfoForm, ProjectInfoQuery, ProjectInfoVO } from '@/api/itsm/projectInfo/types';
import { getProjectInfo, listProjectInfo } from '@/api/itsm/projectInfo';

const props = defineProps({
  project: {
    type: Object as PropType<ProjectInfoVO>,
    required: true
  }
});

const loading = ref(true);

const total = ref(0);

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { project_status } = toRefs<any>(proxy?.useDict('project_status'));

const projectInfoList = ref<ProjectInfoVO[]>([]);

const ids = ref<Array<string | number>>([]);

const isViewMode = ref(false);

const initFormData: ProjectInfoForm = {
  projectId: undefined,
  projectName: undefined,
  projectStatus: undefined,
  constructRespDept: undefined,
  deptManager: undefined,
  deptManagerPhone: undefined,
  workLinkman: undefined,
  workLinkmanPhone: undefined,
  planAcceptanceDate: undefined,
  planOnlineDate: undefined,
  projectTotalAmount: undefined,
  infoSecurityInvest: undefined,
  projectContent: undefined
};

const data = reactive<PageData<ProjectInfoForm, ProjectInfoQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    projectId: undefined,
    projectName: undefined,
    projectStatus: undefined,
    constructRespDept: undefined,
    deptManager: undefined,
    deptManagerPhone: undefined,
    workLinkman: undefined,
    workLinkmanPhone: undefined,
    planAcceptanceDate: undefined,
    planOnlineDate: undefined,
    projectTotalAmount: undefined,
    infoSecurityInvest: undefined,
    projectContent: undefined,
    params: {}
  }
});
const { queryParams, form } = toRefs(data);

const projectInfoFormRef = ref<ElFormInstance>();

// 所选择的项目 id
const selectedProjectId = ref('');

/** 查询项目信息列表 */
const getList = async () => {
  loading.value = true;
  queryParams.value.projectId = props.project.projectId;
  const res = await listProjectInfo(queryParams.value);
  projectInfoList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

const fetchData = () => {
  getList();
};
watch(
  () => props.project.projectId,
  (newId) => {
    if (newId) {
      fetchData();
    }
  },
  { immediate: true }
);

/** 查看按钮操作 */
const handleView = async (row?: ProjectInfoVO) => {
  loading.value = true;
  reset();
  const _projectId = row?.projectId || ids.value[0];
  const res = await getProjectInfo(_projectId);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.showBtn = false;
  dialog.title = '项目信息详细';
  selectedProjectId.value = _projectId;
  loading.value = false;
  isViewMode.value = true;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  projectInfoFormRef.value?.resetFields();
  isViewMode.value = false;
};

const dialog = reactive<DialogOption>({
  visible: false,
  title: '',
  showBtn: true
});

onMounted(() => {
  getList();
});
</script>

<style scoped>
/* 可以在这里添加一些样式 */
</style>
