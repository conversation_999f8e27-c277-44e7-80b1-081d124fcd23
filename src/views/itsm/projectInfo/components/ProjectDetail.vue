<template>
  <el-dialog v-model:visible="dialogVisible" title="项目详情" width="80%" @closed="onClosed">
    <el-tabs v-model="activeTab" @tab-click="handleTabClick">
      <el-tab-pane label="项目信息管理" name="projectInfo">
        <ProjectInfoTab />
      </el-tab-pane>
      <el-tab-pane label="系统信息管理" name="sysInfo">
        <!--        <SysInfoTab />-->
      </el-tab-pane>
      <el-tab-pane label="服务器信息管理" name="serverInfo">
        <!--        <ServerInfoTab />-->
      </el-tab-pane>
      <el-tab-pane label="项目人员管理" name="projectPersonnel">
        <!--        <ProjectPersonnelTab />-->
      </el-tab-pane>
    </el-tabs>
  </el-dialog>
</template>

<script setup lang="ts">
import ProjectInfoTab from './ProjectInfoTab.vue';
import SysInfoTab from './SysInfoTab.vue';
import ServerInfoTab from './ServerInfoTab.vue';
import ProjectPersonnelTab from './ProjectUserTab.vue';
import { ProjectInfoVO } from '@/api/itsm/projectInfo/types';

interface Props {
  project?: ProjectInfoVO;

  visible: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  project: null,
  visible: false
});

const dialogVisible = ref(props.visible);
const activeTab = ref('projectInfo');

// 监听 `visible` 属性的变化
watch(
  () => props.visible,
  (newVal) => {
    dialogVisible.value = newVal;
    console.log('dialogVisible updated to:', dialogVisible.value); // 确认 dialogVisible 被设置为 true
  }
);

const emit = defineEmits(['update:visible']);
const onClosed = () => {
  dialogVisible.value = false;
  activeTab.value = 'projectInfo';

  // 更新父组件的 `visible` 属性
  emit('update:visible', false);
};

const dialog = {
  visible: false,
  title: '',
  showBtn: true
};

const handleTabClick = (tab: any) => {
  // 根据选中的标签页执行相应的API请求
  switch (tab.props.name) {
    case 'projectInfo':
      fetchProjectInfo(props.project.projectId);
      break;
    case 'sysInfo':
      // fetchSysInfo(props.project.id);
      break;
    case 'serverInfo':
      // fetchServerInfo(props.project.id);
      break;
    case 'projectPersonnel':
      // fetchProjectPersonnel(props.project.id);
      break;
    default:
      break;
  }
};

const fetchProjectInfo = async (projectId: string) => {
  // 调用后端接口获取项目信息
  // 示例调用，实际调用请替换为真实接口
  // const response = await fetch(`/api/project/${projectId}/info`);
  // const data = await response.json();
  // console.log(data); // 处理返回的数据
};

const fetchSysInfo = async (projectId: string) => {
  // 获取系统信息
};

const fetchServerInfo = async (projectId: string) => {
  // 获取服务器信息
};

const fetchProjectPersonnel = async (projectId: string) => {
  // 获取项目人员信息
};

onMounted(() => {
  if (props.project?.projectId) {
    fetchProjectInfo(props.project.projectId);
    fetchSysInfo(props.project.projectId);
    fetchServerInfo(props.project.projectId);
    fetchProjectPersonnel(props.project.projectId);
  }
});
</script>

<style lang="scss" scoped>
.el-dialog {
  display: block !important; /* 确保对话框可见 */
  position: absolute !important; /* 确保对话框位置正确 */
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
</style>
