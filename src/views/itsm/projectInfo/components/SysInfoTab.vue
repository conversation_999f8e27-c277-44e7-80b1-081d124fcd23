<template>
  <el-table v-loading="loading" :data="systemInfoList">
    <el-table-column label="序号" align="center" fixed="left" width="55">
      <template #default="scope">
        <span>{{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}</span>
      </template>
    </el-table-column>
    <el-table-column label="系统名称" align="center" prop="systemName" fixed="left" width="300" />
    <el-table-column label="所属项目" align="center" prop="projectId" width="300">
      <template #default="scope">
        <div v-for="project in projectInfoList">
          <span v-if="project.projectId === scope.row.projectId">{{ project.projectName }}</span>
        </div>
      </template>
    </el-table-column>
    <el-table-column label="系统状态" align="center" prop="systemStatus">
      <template #default="scope">
        <dict-tag :options="system_status" :value="scope.row.systemStatus" />
      </template>
    </el-table-column>
    <el-table-column label="建设责任部门" align="center" prop="constructRespDept" />
    <el-table-column label="部门负责人" align="center" prop="deptManager" />
    <el-table-column label="负责人电话" align="center" prop="deptManagerPhone" />
    <el-table-column label="工作联系人" align="center" prop="workLinkman" />
    <el-table-column label="联系人电话" align="center" prop="workLinkmanPhone" />
    <el-table-column label="计划验收时间" align="center" prop="planAcceptanceDate" width="120">
      <template #default="scope">
        <span>{{ parseTime(scope.row.planAcceptanceDate, '{y}-{m}-{d}') }}</span>
      </template>
    </el-table-column>
    <el-table-column label="计划上线时间" align="center" prop="planOnlineDate" width="120">
      <template #default="scope">
        <span>{{ parseTime(scope.row.planOnlineDate, '{y}-{m}-{d}') }}</span>
      </template>
    </el-table-column>
    <el-table-column label="系统内容描述" align="center" prop="systemContent" />
    <el-table-column label="系统类型" align="center" prop="systemType">
      <template #default="scope">
        <dict-tag :options="system_type" :value="scope.row.systemType" />
      </template>
    </el-table-column>
    <el-table-column label="系统架构" align="center" prop="systemArchitecture">
      <template #default="scope">
        <dict-tag :options="system_architecture" :value="scope.row.systemArchitecture" />
      </template>
    </el-table-column>
    <el-table-column label="客户端类型" align="center" prop="clientType">
      <template #default="scope">
        <dict-tag :options="client_type" :value="scope.row.clientType" />
      </template>
    </el-table-column>
    <el-table-column label="系统面向对象" align="center" prop="systemObject">
      <template #default="scope">
        <dict-tag :options="system_object" :value="scope.row.systemObject" />
      </template>
    </el-table-column>
    <el-table-column label="接入网络类型" align="center" prop="networkType">
      <template #default="scope">
        <dict-tag :options="network_type" :value="scope.row.networkType" />
      </template>
    </el-table-column>
    <el-table-column label="是否完全国产化" align="center" prop="localizationFlag">
      <template #default="scope">
        <dict-tag :options="sys_yes_no" :value="scope.row.localizationFlag" />
      </template>
    </el-table-column>
    <el-table-column label="计划改造时间" align="center" prop="planChangeDate" width="120">
      <template #default="scope">
        <span>{{ parseTime(scope.row.planChangeDate, '{y}-{m}-{d}') }}</span>
      </template>
    </el-table-column>
    <el-table-column label="系统风险等级评估" align="center" prop="systemRiskAssessment">
      <template #default="scope">
        <dict-tag :options="system_risk_assessment" :value="scope.row.systemRiskAssessment" />
      </template>
    </el-table-column>
    <el-table-column label="是否属于政务网站或政务新媒体" align="center" prop="govSystemFlag">
      <template #default="scope">
        <dict-tag :options="sys_yes_no" :value="scope.row.govSystemFlag" />
      </template>
    </el-table-column>
    <el-table-column label="特殊情况说明" align="center" prop="specialExplanation" width="300" />
    <el-table-column label="是否与省有应用/数据交互" align="center" prop="proExchangeFlag">
      <template #default="scope">
        <dict-tag :options="sys_yes_no" :value="scope.row.proExchangeFlag" />
      </template>
    </el-table-column>
    <el-table-column label="是否有移动端" align="center" prop="mobileAppFlag">
      <template #default="scope">
        <dict-tag :options="sys_yes_no" :value="scope.row.mobileAppFlag" />
      </template>
    </el-table-column>
  </el-table>

  <pagination v-show="total > 0" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" :total="total" @pagination="getList" />
</template>

<script setup lang="ts">
import { SystemInfoForm, SystemInfoQuery, SystemInfoVO } from '@/api/itsm/systemInfo/types';
import { ProjectInfoVO } from '@/api/itsm/projectInfo/types';
import { listSystemInfo } from '@/api/itsm/systemInfo';
import { listProjectInfo } from '@/api/itsm/projectInfo';
import { PropType } from 'vue';

const props = defineProps({
  project: {
    type: Object as PropType<ProjectInfoVO>,
    required: true
  }
});

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { network_type, system_risk_assessment, system_object, system_type, sys_yes_no, system_architecture, system_status, client_type } = toRefs<any>(
  proxy?.useDict(
    'network_type',
    'system_risk_assessment',
    'system_object',
    'system_type',
    'sys_yes_no',
    'system_architecture',
    'system_status',
    'client_type'
  )
);

const systemInfoList = ref<SystemInfoVO[]>([]);

const loading = ref(true);
const total = ref(0);

// 项目信息
const projectInfoList = ref<ProjectInfoVO[]>([]);

const initFormData: SystemInfoForm = {
  systemId: undefined,
  projectId: undefined,
  systemName: undefined,
  systemStatus: undefined,
  constructRespDept: undefined,
  deptManager: undefined,
  deptManagerPhone: undefined,
  workLinkman: undefined,
  workLinkmanPhone: undefined,
  planAcceptanceDate: undefined,
  planOnlineDate: undefined,
  systemContent: undefined,
  systemType: undefined,
  systemArchitecture: undefined,
  clientType: undefined,
  systemObject: undefined,
  networkType: undefined,
  localizationFlag: undefined,
  planChangeDate: undefined,
  systemRiskAssessment: undefined,
  govSystemFlag: undefined,
  specialExplanation: undefined,
  proExchangeFlag: undefined,
  mobileAppFlag: undefined
};
const data = reactive<PageData<SystemInfoForm, SystemInfoQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    projectId: undefined,
    systemName: undefined,
    systemStatus: undefined,
    constructRespDept: undefined,
    deptManager: undefined,
    deptManagerPhone: undefined,
    workLinkman: undefined,
    workLinkmanPhone: undefined,
    planAcceptanceDate: undefined,
    planOnlineDate: undefined,
    systemContent: undefined,
    systemType: undefined,
    systemArchitecture: undefined,
    clientType: undefined,
    systemObject: undefined,
    networkType: undefined,
    localizationFlag: undefined,
    planChangeDate: undefined,
    systemRiskAssessment: undefined,
    govSystemFlag: undefined,
    specialExplanation: undefined,
    proExchangeFlag: undefined,
    mobileAppFlag: undefined,
    params: {}
  },
  rules: {
    systemId: [{ required: true, message: '系统id不能为空', trigger: 'blur' }],
    projectId: [{ required: true, message: '所属项目id不能为空', trigger: 'blur' }],
    systemName: [{ required: true, message: '系统名称不能为空', trigger: 'blur' }],
    systemStatus: [{ required: true, message: '系统状态不能为空', trigger: 'change' }],
    constructRespDept: [{ required: true, message: '建设责任部门不能为空', trigger: 'blur' }],
    deptManager: [{ required: true, message: '部门负责人不能为空', trigger: 'blur' }],
    deptManagerPhone: [{ required: true, message: '负责人电话不能为空', trigger: 'blur' }],
    workLinkman: [{ required: true, message: '工作联系人不能为空', trigger: 'blur' }],
    workLinkmanPhone: [{ required: true, message: '联系人电话不能为空', trigger: 'blur' }],
    planAcceptanceDate: [{ required: true, message: '计划验收时间不能为空', trigger: 'blur' }],
    planOnlineDate: [{ required: true, message: '计划上线时间不能为空', trigger: 'blur' }],
    systemContent: [{ required: true, message: '系统内容描述不能为空', trigger: 'blur' }],
    systemType: [{ required: true, message: '系统类型不能为空', trigger: 'change' }],
    systemArchitecture: [{ required: true, message: '系统架构不能为空', trigger: 'change' }],
    clientType: [{ required: true, message: '客户端类型不能为空', trigger: 'change' }],
    systemObject: [{ required: true, message: '系统面向对象不能为空', trigger: 'change' }],
    networkType: [{ required: true, message: '接入网络类型不能为空', trigger: 'change' }],
    localizationFlag: [{ required: true, message: '是否完全国产化不能为空', trigger: 'change' }],
    planChangeDate: [{ required: true, message: '计划改造时间不能为空', trigger: 'blur' }],
    systemRiskAssessment: [{ required: true, message: '系统风险等级评估不能为空', trigger: 'change' }],
    govSystemFlag: [{ required: true, message: '是否属于政务网站或政务新媒体不能为空', trigger: 'change' }],
    specialExplanation: [{ required: true, message: '特殊情况说明不能为空', trigger: 'blur' }],
    proExchangeFlag: [{ required: true, message: '是否与省有应用/数据交互不能为空', trigger: 'change' }],
    mobileAppFlag: [{ required: true, message: '是否有移动端不能为空', trigger: 'change' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询系统信息列表 */
const getList = async () => {
  loading.value = true;
  queryParams.value.projectId = props.project.projectId;
  const res = await listSystemInfo(queryParams.value);
  systemInfoList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

const fetchData = () => {
  getList();
};

watch(
  () => props.project.projectId,
  (newId) => {
    if (newId) {
      fetchData();
    }
  },
  { immediate: true }
);

//项目信息
const getProjectInfoList = async () => {
  const res = await listProjectInfo({});
  projectInfoList.value = res.rows;
};

onMounted(() => {
  getList();
  getProjectInfoList();
});
</script>
