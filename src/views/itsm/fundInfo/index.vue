<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="180px">
            <el-form-item label="所属项目" prop="projectId">
              <el-select v-model="queryParams.projectId" placeholder="请选择所属项目" clearable>
                <el-option v-for="dict in allProjectInfoList" :key="dict.projectId" :label="dict.projectName" :value="dict.projectId" />
              </el-select>
            </el-form-item>
            <el-form-item label="款项批次" prop="paymentBatch">
              <el-input v-model="queryParams.paymentBatch" placeholder="请输入款项批次" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="计划付款金额" prop="planAmount">
              <el-input v-model="queryParams.planAmount" placeholder="请输入计划付款金额" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="计划付款日期" prop="planPaymentTime">
              <el-date-picker
                v-model="queryParams.planPaymentTime"
                clearable
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="请选择计划付款日期"
              />
            </el-form-item>
            <el-form-item label="已付款金额" prop="paidAmount">
              <el-input v-model="queryParams.paidAmount" placeholder="请输入已付款金额" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="实际付款日期" prop="actualPaymentTime">
              <el-date-picker
                v-model="queryParams.actualPaymentTime"
                clearable
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="请选择实际付款日期"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button v-hasPermi="['itsm:fundInfo:add']" type="primary" plain icon="Plus" @click="handleAdd">新增 </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['itsm:fundInfo:edit']" type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()"
              >修改
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['itsm:fundInfo:remove']" type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()"
              >删除
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['itsm:fundInfo:export']" type="warning" plain icon="Download" @click="handleExport"> 导出 </el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @query-table="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="fundInfoList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="序号" align="center" fixed="left">
          <template #default="scope">
            <span>{{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}</span>
          </template>
        </el-table-column>
        <!--        <el-table-column label="资金id" align="center" prop="fundId" v-if="true" />-->
        <el-table-column label="所属项目" align="center" prop="projectId">
          <template #default="scope">
            <div v-for="project in allProjectInfoList">
              <span v-if="project.projectId === scope.row.projectId">{{ project.projectName }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="款项批次" align="center" prop="paymentBatch" />
        <el-table-column label="计划付款金额" align="center" prop="planAmount" />
        <el-table-column label="计划付款日期" align="center" prop="planPaymentTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.planPaymentTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="已付款金额" align="center" prop="paidAmount" />
        <el-table-column label="实际付款日期" align="center" prop="actualPaymentTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.actualPaymentTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button v-hasPermi="['itsm:fundInfo:edit']" link type="primary" icon="Edit" @click="handleUpdate(scope.row)"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button v-hasPermi="['itsm:fundInfo:remove']" link type="primary" icon="Delete" @click="handleDelete(scope.row)"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" :total="total" @pagination="getList" />
    </el-card>
    <!-- 添加或修改资金信息对话框 -->
    <el-dialog v-model="dialog.visible" :title="dialog.title" width="500px" append-to-body>
      <el-form ref="fundInfoFormRef" :model="form" :rules="rules" label-width="150px">
        <el-form-item label="所属项目" prop="projectId">
          <el-select v-model="form.projectId" placeholder="请选择所属项目" clearable>
            <el-option v-for="dict in allProjectInfoList" :key="dict.projectId" :label="dict.projectName" :value="dict.projectId" />
          </el-select>
        </el-form-item>
        <el-form-item label="款项批次" prop="paymentBatch">
          <el-input v-model="form.paymentBatch" placeholder="请输入款项批次" />
        </el-form-item>
        <el-form-item label="计划付款金额" prop="planAmount">
          <el-input v-model="form.planAmount" placeholder="请输入计划付款金额" />
        </el-form-item>
        <el-form-item label="计划付款日期" prop="planPaymentTime">
          <el-date-picker
            v-model="form.planPaymentTime"
            clearable
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择计划付款日期"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="已付款金额" prop="paidAmount">
          <el-input v-model="form.paidAmount" placeholder="请输入已付款金额" />
        </el-form-item>
        <el-form-item label="实际付款日期" prop="actualPaymentTime">
          <el-date-picker
            v-model="form.actualPaymentTime"
            clearable
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择实际付款日期"
          >
          </el-date-picker>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="FundInfo" lang="ts">
import { addFundInfo, delFundInfo, getFundInfo, listFundInfo, updateFundInfo } from '@/api/itsm/fundInfo';
import { FundInfoForm, FundInfoQuery, FundInfoVO } from '@/api/itsm/fundInfo/types';
import { ProjectInfoVO } from '@/api/itsm/projectInfo/types';
import { listProjectInfo } from '@/api/itsm/projectInfo';
import { ItsmAllProjectInfoVo } from '@/api/itsm/projectUser/types';
import { getItsmAllProjectInfo } from '@/api/itsm/projectUser';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const fundInfoList = ref<FundInfoVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const fundInfoFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: FundInfoForm = {
  fundId: undefined,
  projectId: undefined,
  paymentBatch: undefined,
  planAmount: undefined,
  planPaymentTime: undefined,
  paidAmount: undefined,
  actualPaymentTime: undefined
};
const data = reactive<PageData<FundInfoForm, FundInfoQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    projectId: undefined,
    paymentBatch: undefined,
    planAmount: undefined,
    planPaymentTime: undefined,
    paidAmount: undefined,
    actualPaymentTime: undefined,
    params: {}
  },
  rules: {
    fundId: [{ required: true, message: '资金id不能为空', trigger: 'blur' }],
    projectId: [{ required: true, message: '所属项目id不能为空', trigger: 'blur' }],
    paymentBatch: [{ required: true, message: '款项批次不能为空', trigger: 'blur' }],
    planAmount: [{ required: true, message: '计划付款金额不能为空', trigger: 'blur' }],
    planPaymentTime: [{ required: true, message: '计划付款日期不能为空', trigger: 'blur' }],
    paidAmount: [{ required: true, message: '已付款金额不能为空', trigger: 'blur' }],
    actualPaymentTime: [{ required: true, message: '实际付款日期不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

// 项目信息
const projectInfoList = ref<ProjectInfoVO[]>([]);

const seachLoading = ref(true);

/** 查询资金信息列表 */
const getList = async () => {
  loading.value = true;
  const res = await listFundInfo(queryParams.value);
  fundInfoList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  fundInfoFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: FundInfoVO[]) => {
  ids.value = selection.map((item) => item.fundId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加资金信息';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: FundInfoVO) => {
  reset();
  const _fundId = row?.fundId || ids.value[0];
  const res = await getFundInfo(_fundId);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改资金信息';
};

/** 提交按钮 */
const submitForm = () => {
  fundInfoFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.fundId) {
        await updateFundInfo(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addFundInfo(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: FundInfoVO) => {
  const _fundIds = row?.fundId || ids.value;
  await proxy?.$modal.confirm('是否确认删除资金信息编号为"' + _fundIds + '"的数据项？').finally(() => (loading.value = false));
  await delFundInfo(_fundIds);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

// 格式化日期的函数
const formatCurrentDate = (format) => {
  const date = new Date();
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');

  if (format === 'YYYY-MM-DD HH:mm:ss') {
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  } else {
    throw new Error('Unsupported date format');
  }
};

/** 导出按钮操作 */
const handleExport = () => {
  const formattedDate = formatCurrentDate('YYYY-MM-DD HH:mm:ss');
  proxy?.download(
    'itsm/fundInfo/export',
    {
      ...queryParams.value
    },
    `资金信息_${formattedDate}.xlsx`
  );
};

const getProjectInfoList = async () => {
  const res = await listProjectInfo({});
  projectInfoList.value = res.rows;
};

//项目，系统，人员
const searchLoading = ref(true);
const allProjectInfoList = ref<ItsmAllProjectInfoVo[]>([]);
const getItsmAllProjectInfoVo = async () => {
  searchLoading.value = true;
  //这里查询一下项目，人员，以及所属人员
  const res = await getItsmAllProjectInfo();
  allProjectInfoList.value = res.data;
  searchLoading.value = false;
};

onMounted(() => {
  getList();
  // getProjectInfoList();
  getItsmAllProjectInfoVo();
});
</script>
