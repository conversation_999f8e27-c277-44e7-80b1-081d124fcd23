<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="120px">
            <el-form-item label="所属系统" prop="systemId">
              <el-select v-model="queryParams.systemId" placeholder="请选择所属系统" clearable>
                <el-option v-for="dict in systemInfoList" :key="dict.systemId" :label="dict.systemName" :value="dict.systemId" />
              </el-select>
            </el-form-item>
            <el-form-item label="服务器名称" prop="serverName">
              <el-input v-model="queryParams.serverName" placeholder="请输入服务器名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="服务器类型" prop="serverType">
              <el-select v-model="queryParams.serverType" placeholder="请选择服务器类型" clearable>
                <el-option v-for="dict in server_type" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="党政网络ip" prop="govNetworkIp">
              <el-input v-model="queryParams.govNetworkIp" placeholder="请输入党政网络ip" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="互联网ip" prop="internetIp">
              <el-input v-model="queryParams.internetIp" placeholder="请输入互联网ip" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="申请工单号" prop="applyOrderNo">
              <el-input v-model="queryParams.applyOrderNo" placeholder="请输入申请工单号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button v-hasPermi="['itsm:serverInfo:add']" type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['itsm:serverInfo:edit']" type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['itsm:serverInfo:remove']" type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['itsm:serverInfo:export']" type="warning" plain icon="Download" @click="handleExport">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @query-table="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="serverInfoList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="序号" align="center" fixed="left" width="55">
          <template #default="scope">
            <span>{{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column label="所属系统" align="center" prop="systemId" fixed="left" width="200">
          <template #default="scope">
            <div v-for="system in systemInfoList">
              <span v-if="system.systemId === scope.row.systemId">{{ system.systemName }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="服务器名称" align="center" prop="serverName" fixed="left" width="200" />
        <el-table-column label="服务器类型" align="center" prop="serverType" width="120">
          <template #default="scope">
            <dict-tag :options="server_type" :value="scope.row.serverType" />
          </template>
        </el-table-column>
        <el-table-column label="其他类型" align="center" prop="otherType" />
        <el-table-column label="是否部署中山市政务云平台" align="center" prop="zsGovCloudFlag">
          <template #default="scope">
            <dict-tag :options="sys_yes_no" :value="scope.row.zsGovCloudFlag" />
          </template>
        </el-table-column>
        <el-table-column label="云主机托管位置" align="center" prop="cloudLocation" />
        <el-table-column label="操作系统名称及版本" align="center" prop="osNameVersion" />
        <el-table-column label="开发工具" align="center" prop="devTool" />
        <el-table-column label="数据库名称及版本" align="center" prop="dbNameVersion" />
        <el-table-column label="数据库容量" align="center" prop="dbCapacity" />
        <el-table-column label="中间件名称及版本" align="center" prop="middlewareNameVersion" />
        <el-table-column label="其他软件" align="center" prop="otherSoftware" />
        <el-table-column label="党政网络ip" align="center" prop="govNetworkIp" width="120" />
        <el-table-column label="互联网ip" align="center" prop="internetIp" width="120" />
        <el-table-column label="云主机ipv4地址" align="center" prop="cloudServerIpv4" width="120" />
        <el-table-column label="云主机ipv6地址" align="center" prop="cloudServerIpv6" width="120" />
        <el-table-column label="互联网访问地址" align="center" prop="internetUrl" />
        <el-table-column label="政务网络访问地址" align="center" prop="govNetworkUrl" />
        <el-table-column label="系统后台地址" align="center" prop="systemAdminUrl" />
        <el-table-column label="cpu" align="center" prop="cpu" />
        <el-table-column label="内存" align="center" prop="memory" />
        <el-table-column label="系统盘" align="center" prop="systemHardDisk" />
        <el-table-column label="数据盘" align="center" prop="dataHardDisk" />
        <el-table-column label="申请工单号" align="center" prop="applyOrderNo" />
        <el-table-column label="开通端口信息" align="center" prop="openPortInfo" />
        <el-table-column label="端口用途" align="center" prop="openPortUse" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button v-hasPermi="['itsm:serverInfo:edit']" link type="primary" icon="Edit" @click="handleUpdate(scope.row)"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button v-hasPermi="['itsm:serverInfo:remove']" link type="danger" icon="Delete" @click="handleDelete(scope.row)"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" :total="total" @pagination="getList" />
    </el-card>
    <!-- 添加或修改服务器信息对话框 -->
    <el-dialog v-model="dialog.visible" :title="dialog.title" width="800px" append-to-body>
      <el-form ref="serverInfoFormRef" :model="form" :rules="rules" label-width="200px">
        <el-form-item label="所属系统" prop="systemId">
          <!--          <el-input v-model="form.systemId" placeholder="请输入所属系统id" />-->
          <el-select v-model="form.systemId" placeholder="请选择所属系统" clearable>
            <el-option v-for="dict in systemInfoList" :key="dict.systemId" :label="dict.systemName" :value="dict.systemId" />
          </el-select>
        </el-form-item>
        <el-form-item label="服务器名称" prop="serverName">
          <el-input v-model="form.serverName" placeholder="请输入服务器名称" />
        </el-form-item>
        <el-form-item label="服务器类型" prop="serverType">
          <el-select v-model="form.serverType" placeholder="请选择服务器类型">
            <el-option v-for="dict in server_type" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="其他类型" prop="otherType">
          <el-input v-model="form.otherType" placeholder="请输入其他类型" />
        </el-form-item>
        <el-form-item label="是否部署中山市政务云平台" prop="zsGovCloudFlag">
          <el-select v-model="form.zsGovCloudFlag" placeholder="请选择是否部署中山市政务云平台">
            <el-option v-for="dict in sys_yes_no" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="云主机托管位置" prop="cloudLocation">
          <el-input v-model="form.cloudLocation" placeholder="请输入云主机托管位置" />
        </el-form-item>
        <el-form-item label="操作系统名称及版本" prop="osNameVersion">
          <el-input v-model="form.osNameVersion" placeholder="请输入操作系统名称及版本" />
        </el-form-item>
        <el-form-item label="开发工具" prop="devTool">
          <el-input v-model="form.devTool" placeholder="请输入开发工具" />
        </el-form-item>
        <el-form-item label="数据库名称及版本" prop="dbNameVersion">
          <el-input v-model="form.dbNameVersion" placeholder="请输入数据库名称及版本" />
        </el-form-item>
        <el-form-item label="数据库容量" prop="dbCapacity">
          <el-input v-model="form.dbCapacity" placeholder="请输入数据库容量" />
        </el-form-item>
        <el-form-item label="中间件名称及版本" prop="middlewareNameVersion">
          <el-input v-model="form.middlewareNameVersion" placeholder="请输入中间件名称及版本" />
        </el-form-item>
        <el-form-item label="其他软件" prop="otherSoftware">
          <el-input v-model="form.otherSoftware" placeholder="请输入其他软件" />
        </el-form-item>
        <el-form-item label="党政网络ip" prop="govNetworkIp">
          <el-input v-model="form.govNetworkIp" placeholder="请输入党政网络ip" />
        </el-form-item>
        <el-form-item label="互联网ip" prop="internetIp">
          <el-input v-model="form.internetIp" placeholder="请输入互联网ip" />
        </el-form-item>
        <el-form-item label="云主机ipv4地址" prop="cloudServerIpv4">
          <el-input v-model="form.cloudServerIpv4" placeholder="请输入云主机ipv4地址" style="width: 60%" />
          <el-button style="margin-left: 10px" type="primary" :loading="loading" @click="handleSyncServerIndex">获取服务器指标</el-button>
        </el-form-item>
        <el-form-item label="云主机ipv6地址" prop="cloudServerIpv6">
          <el-input v-model="form.cloudServerIpv6" placeholder="请输入云主机ipv6地址" />
        </el-form-item>
        <el-form-item label="互联网访问地址" prop="internetUrl">
          <el-input v-model="form.internetUrl" placeholder="请输入互联网访问地址" />
        </el-form-item>
        <el-form-item label="政务网络访问地址" prop="govNetworkUrl">
          <el-input v-model="form.govNetworkUrl" placeholder="请输入政务网络访问地址" />
        </el-form-item>
        <el-form-item label="系统后台地址" prop="systemAdminUrl">
          <el-input v-model="form.systemAdminUrl" placeholder="请输入系统后台地址" />
        </el-form-item>
        <el-form-item label="cpu(核)" prop="cpu">
          <el-input v-model="form.cpu" placeholder="请输入cpu" />
        </el-form-item>
        <el-form-item label="内存(G)" prop="memory">
          <el-input v-model="form.memory" placeholder="请输入内存" />
        </el-form-item>
        <el-form-item label="系统盘(G)" prop="systemHardDisk">
          <el-input v-model="form.systemHardDisk" placeholder="请输入系统盘" />
        </el-form-item>
        <el-form-item label="数据盘(G)" prop="dataHardDisk">
          <el-input v-model="form.dataHardDisk" placeholder="请输入数据盘" />
        </el-form-item>
        <el-form-item label="申请工单号" prop="applyOrderNo">
          <el-input v-model="form.applyOrderNo" placeholder="请输入申请工单号" />
        </el-form-item>
        <el-form-item label="开通端口信息" prop="openPortInfo">
          <el-input v-model="form.openPortInfo" placeholder="请输入开通端口信息" />
        </el-form-item>
        <el-form-item label="端口用途" prop="openPortUse">
          <el-input v-model="form.openPortUse" placeholder="请输入端口用途" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="ServerInfo" lang="ts">
import { addServerInfo, delServerInfo, getServerInfo, listServerInfo, syncServerIndex, updateServerInfo } from '@/api/itsm/serverInfo';
import { ServerInfoForm, ServerInfoQuery, ServerInfoVO } from '@/api/itsm/serverInfo/types';
import { listSystemInfo } from '@/api/itsm/systemInfo';
import { SystemInfoQuery, SystemInfoVO } from '@/api/itsm/systemInfo/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { sys_yes_no, server_type } = toRefs<any>(proxy?.useDict('sys_yes_no', 'server_type'));

const serverInfoList = ref<ServerInfoVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const serverInfoFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: ServerInfoForm = {
  serverId: undefined,
  systemId: undefined,
  serverName: undefined,
  serverType: undefined,
  otherType: undefined,
  zsGovCloudFlag: undefined,
  cloudLocation: undefined,
  osNameVersion: undefined,
  devTool: undefined,
  dbNameVersion: undefined,
  dbCapacity: undefined,
  middlewareNameVersion: undefined,
  otherSoftware: undefined,
  govNetworkIp: undefined,
  internetIp: undefined,
  cloudServerIpv4: undefined,
  cloudServerIpv6: undefined,
  internetUrl: undefined,
  govNetworkUrl: undefined,
  systemAdminUrl: undefined,
  cpu: undefined,
  memory: undefined,
  systemHardDisk: undefined,
  dataHardDisk: undefined,
  applyOrderNo: undefined,
  openPortInfo: undefined,
  openPortUse: undefined
};
const data = reactive<PageData<ServerInfoForm, ServerInfoQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    systemId: undefined,
    serverName: undefined,
    serverType: undefined,
    otherType: undefined,
    zsGovCloudFlag: undefined,
    cloudLocation: undefined,
    osNameVersion: undefined,
    devTool: undefined,
    dbNameVersion: undefined,
    dbCapacity: undefined,
    middlewareNameVersion: undefined,
    otherSoftware: undefined,
    govNetworkIp: undefined,
    internetIp: undefined,
    cloudServerIpv4: undefined,
    cloudServerIpv6: undefined,
    internetUrl: undefined,
    govNetworkUrl: undefined,
    systemAdminUrl: undefined,
    cpu: undefined,
    memory: undefined,
    systemHardDisk: undefined,
    dataHardDisk: undefined,
    applyOrderNo: undefined,
    openPortInfo: undefined,
    openPortUse: undefined,
    params: {}
  },
  rules: {
    serverId: [{ required: true, message: '服务器id不能为空', trigger: 'blur' }],
    systemId: [{ required: true, message: '所属系统id不能为空', trigger: 'blur' }],
    serverName: [{ required: true, message: '服务器名称不能为空', trigger: 'blur' }],
    serverType: [{ required: true, message: '服务器类型不能为空', trigger: 'change' }],
    otherType: [{ required: false, message: '其他类型不能为空', trigger: 'blur' }],
    zsGovCloudFlag: [{ required: true, message: '是否部署中山市政务云平台不能为空', trigger: 'change' }],
    cloudLocation: [{ required: false, message: '云主机托管位置不能为空', trigger: 'blur' }],
    osNameVersion: [{ required: true, message: '操作系统名称及版本不能为空', trigger: 'blur' }],
    devTool: [{ required: false, message: '开发工具不能为空', trigger: 'blur' }],
    dbNameVersion: [{ required: false, message: '数据库名称及版本不能为空', trigger: 'blur' }],
    dbCapacity: [{ required: false, message: '数据库容量不能为空', trigger: 'blur' }],
    middlewareNameVersion: [{ required: false, message: '中间件名称及版本不能为空', trigger: 'blur' }],
    otherSoftware: [{ required: false, message: '其他软件不能为空', trigger: 'blur' }],
    govNetworkIp: [{ required: false, message: '党政网络ip不能为空', trigger: 'blur' }],
    internetIp: [{ required: false, message: '互联网ip不能为空', trigger: 'blur' }],
    cloudServerIpv4: [{ required: false, message: '云主机ipv4地址不能为空', trigger: 'blur' }],
    cloudServerIpv6: [{ required: false, message: '云主机ipv6地址不能为空', trigger: 'blur' }],
    internetUrl: [{ required: false, message: '互联网访问地址不能为空', trigger: 'blur' }],
    govNetworkUrl: [{ required: false, message: '政务网络访问地址不能为空', trigger: 'blur' }],
    systemAdminUrl: [{ required: false, message: '系统后台地址不能为空', trigger: 'blur' }],
    cpu: [{ required: false, message: 'cpu不能为空', trigger: 'blur' }],
    memory: [{ required: false, message: '内存不能为空', trigger: 'blur' }],
    systemHardDisk: [{ required: false, message: '系统盘不能为空', trigger: 'blur' }],
    dataHardDisk: [{ required: false, message: '数据盘不能为空', trigger: 'blur' }],
    applyOrderNo: [{ required: false, message: '申请工单号不能为空', trigger: 'blur' }],
    openPortInfo: [{ required: false, message: '开通端口信息不能为空', trigger: 'blur' }],
    openPortUse: [{ required: false, message: '端口用途不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询服务器信息列表 */
const getList = async () => {
  loading.value = true;
  const res = await listServerInfo(queryParams.value);
  serverInfoList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  serverInfoFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: ServerInfoVO[]) => {
  ids.value = selection.map((item) => item.serverId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加服务器信息';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: ServerInfoVO) => {
  loading.value = true;
  reset();
  const _serverId = row?.serverId || ids.value[0];
  const res = await getServerInfo(_serverId);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改服务器信息';
  loading.value = false;
};

/** 提交按钮 */
const submitForm = () => {
  serverInfoFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.serverId) {
        await updateServerInfo(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addServerInfo(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: ServerInfoVO) => {
  const _serverIds = row?.serverId || ids.value;
  await proxy?.$modal.confirm('是否确认删除服务器信息编号为"' + _serverIds + '"的数据项？').finally(() => (loading.value = false));
  await delServerInfo(_serverIds);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

// 格式化日期的函数
const formatCurrentDate = (format) => {
  const date = new Date();
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');

  if (format === 'YYYY-MM-DD HH:mm:ss') {
    return `${year}${month}${day}${hours}${minutes}${seconds}`;
  } else {
    throw new Error('Unsupported date format');
  }
};

/** 导出按钮操作 */
const handleExport = () => {
  const formattedDate = formatCurrentDate('YYYY-MM-DD HH:mm:ss');
  proxy?.download(
    'itsm/serverInfo/export',
    {
      ...queryParams.value
    },
    `服务器信息_${formattedDate}.xlsx`
  );
};

/**
 * 系统信息列表
 */
const systemInfoList = ref<SystemInfoVO[]>([]);
const getSystemInfoList = async () => {
  const query: SystemInfoQuery = {
    pageNum: 1,
    pageSize: 999,
    params: {}
  };
  const res = await listSystemInfo(query);
  systemInfoList.value = res.rows;
};

/**
 * 读取 Prometheus 服务器指标数据
 */
const handleSyncServerIndex = async () => {
  loading.value = true;
  const res = await syncServerIndex({ cloudServerIpv4: form.value.cloudServerIpv4 }).finally(() => (loading.value = false));
  console.log('handleSyncServerIndex => ', res);
  form.value.cpu = res.data.cpu;
  form.value.memory = res.data.memory;
  form.value.systemHardDisk = res.data.disk;
};

onMounted(() => {
  getList();
  getSystemInfoList();
});
</script>
